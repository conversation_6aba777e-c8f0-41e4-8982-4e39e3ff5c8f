<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        $request->validate([
            'email' => 'required|email|max:255',
        ]);

        $existingUser = User::where('email', $request->input('email'))->first();

        if ($existingUser) {
            if ($existingUser->password) {
                return response()->json(['message' => 'This email is already registered.'], 409);
            } else {
                // Ensure existing token is revoked or not used
                $existingUser->tokens->each(function ($token) {
                    $token->delete();
                });

                // Resume registration with a new token
                $existingToken = $existingUser->createToken('Registering-Email')->accessToken;
                return response()->json([
                    'message' => 'Resume registration.',
                    'token' => $existingToken
                ], 200);
            }
        }

        // New user registration logic
        $isAuthorized = \DB::table('authorized_personnel')->where('email', $request->input('email'))->exists();

        if (!$isAuthorized) {
            return response()->json(['message' => 'The email registered is not for authorized personnel.'], 403);
        }

        $user = User::create([
            'email' => $request->input('email'),
            'password_set' => false,
        ]);

        Auth::login($user);
        $this->sendVerificationCode($user->email);
        $token = $user->createToken('Registering-Email')->accessToken;

        return response()->json([
            'message' => 'A verification code has been sent to your email.',
            'token' => $token
        ]);
    }

    public function isPasswordSet(Request $request)
    {
        $user = $request->user();

        return response()->json(['passwordSet' => !is_null($user->password)]);
    }

    public function getVerificationPage(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            \Log::info('User not authenticated when accessing verification page');
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        return response()->json([
            'email' => $user->email,
            'verification_code' => $user->verification_code,
        ]);
    }

    public function getEmail(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        return response()->json(['email' => $user->email]);
    }
    
    public function checkApprovalStatus()
    {
        $user = Auth::user();
        if ($user->status === 'pending') {
            return response()->json(['approved' => false], 403);
        }

        return response()->json(['approved' => true], 200);
    }

    public function resendVerificationCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $user = User::where('email', $request->input('email'))->first();

        if (!$user) {
            return response()->json(['message' => 'User not found.'], 404);
        }

        // Check if a minute has passed since the last code generation
        if ($user->verification_code_generated_at && now()->diffInMinutes($user->verification_code_generated_at) < 1) {
            return response()->json(['message' => 'You can only request a new code once per minute.'], 429);
        }

        // Resend verification code
        $this->sendVerificationCode($user->email);

        // Log information about the resent verification code
        \Log::info('Verification code resent to user:', ['user_id' => $user->id]);

        return response()->json(['message' => 'A new verification code has been sent to your email.']);
    }

    public function check(Request $request)
    {
        return response()->json([
            'authenticated' => $request->user() ? true : false,
            'status' => $request->user()?->status,
            'is_admin' => $request->user() ? $request->user()->is_admin : false
        ]);
    }

    private function sendVerificationCode($email)
    {
        $verificationCode = rand(100000, 999999);

        $user = User::updateOrCreate(
            ['email' => $email],
            ['verification_code' => $verificationCode, 'verification_code_generated_at' => now()]
        );

        try {
            Mail::to($user->email)->send(new \App\Mail\SendVerificationCode($verificationCode));
        } catch (\Exception $e) {
            \Log::error('Failed to send verification code:', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Failed to send verification code.'], 500);
        }
    }
}
