<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Receipt extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['item', 'date', 'total_item', 'total_pack', 'grand_total'];

    public function inventories()
    {
        return $this->hasMany(Inventory::class, 'receipt_id', 'id');
    }

    public function updateTotals()
    {
        $totalItems = 0;
        $totalPacks = 0;
        $grandTotal = 0;
    
        foreach ($this->inventories as $inventory) {
            $totalItems += $inventory->qty_of_item;
            $totalPacks += $inventory->qty_of_pack;
            $grandTotal += $inventory->totalAmount;
        }
    
        $this->total_item = $totalItems;
        $this->total_pack = $totalPacks;
        $this->grand_total = $grandTotal;
    
        $this->save();
    }

    protected static function booted()
    {
        static::deleted(function ($receipt) {
            foreach ($receipt->inventories as $inventory) {
                $inventory->delete();
            }
        });
    }
}
