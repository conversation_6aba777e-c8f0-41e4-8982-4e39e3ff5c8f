import React, { useState, useEffect } from 'react';
import ReceiptProps from './ReceiptProps';
import { FaSync } from 'react-icons/fa';
import { api } from '../../api/apiService';

export const Receipt = () => {
  const [errors, setErrors] = useState({});
  const [message, setMessage] = useState('');
  const [isError, setIsError] = useState(false);
  const [loading, setLoading] = useState(true)
  const [currentReceipt, setCurrentReceipt] = useState({});
  const [activeTab, setActiveTab] = useState('receipt')

  const handleTabChange = (tab) => setActiveTab(tab)

  useEffect(() => {
    if (message === 'Receipt confirmed successfully.') {
      window.location.reload();
    }
  }, [message]);

  // Calculate the grand total based on the items in the currentReceipt
  const grandTotal = Array.isArray(currentReceipt.items) 
    ? currentReceipt.items.reduce((acc, item) => {
        const totalAmount = parseFloat(item.totalAmount) || 0;
        return acc + totalAmount;
      }, 0) 
    : 0;

  // Handle Confirm action
  const handleConfirm = async () => {
    const requiredFields = [ 'category_name', 'item_name', 'qty_of_item', 'qty_of_pack', 'price', 'totalAmount', 'store', 'receipt_no'];
    const errors = {};
  
    // Check if quantity of items is greater than 0
    if (currentReceipt.items && currentReceipt.items.length > 0 && currentReceipt.items.some((item) => item.qty_of_item <= 0)) {
      errors.qty_of_item = 'Quantity of items must be greater than 0';
    }
  
    if (Array.isArray(currentReceipt.items) && currentReceipt.items.length > 0) {
      requiredFields.forEach((field) => {
        if (!currentReceipt.items[0][field]) {
          errors[field] = 'This field is required';
        }
      });
  
      // Check each item for required fields
      currentReceipt.items.forEach((item, index) => {
        if (item.category_name === 'Select') {
          errors[`category_name_${index}`] = 'Please select a category';
        }
        if (!item.category_name) errors[`category_name_${index}`] = 'Category is required';
        if (!item.item_name) errors[`item_name_${index}`] = 'Item name is required';
        if (!item.qty_of_item) errors[`qty_of_item_${index}`] = 'Quantity of item is required';
        if (!item.qty_of_pack) errors[`qty_of_pack_${index}`] = 'Quantity of pack is required';
        if (!item.price) errors[`price_${index}`] = 'Price is required';
        if (!item.totalAmount) errors[`totalAmount_${index}`] = 'Total amount is required';
        if (!item.store) errors[`store_${index}`] = 'Store is required';
        if (!item.receipt_no) errors[`receipt_no_${index}`] = 'Receipt number is required';
      });
    } else {
      errors.items = 'Please add at least one item';
    }
  
    if (Object.keys(errors).length > 0) {
      setErrors(errors);
      setMessage('Please fill in all required fields.');
      if (errors.qty_of_item) {
        setMessage(errors.qty_of_item);
      }
      setIsError(true);
      setTimeout(() => {
        setErrors({});
        setMessage('');
        setIsError(false);
      }, 5000);
    } else {
      // Prepare receipt data
      const receiptData = {
        date: currentReceipt.date,
        items: currentReceipt.items.map((item) => ({
          category_name: item.category_name,
          item_name: item.item_name,
          qty_of_item: item.qty_of_item,
          qty_of_pack: item.qty_of_pack,
          price: item.price,
          totalAmount: item.totalAmount,
          store: item.store,
          receipt_no: item.receipt_no,
        })),
      };
      
  
      try {
        // Call API to create receipt
        console.log('Receipt created successfully:', receiptData);
        await api.post('/receipts', receiptData);
        setMessage('Receipt confirmed successfully.');        
        setIsError(false);

        // Reset receipt and items after successful confirmation
        setCurrentReceipt({ date: '', items: [] });

      } catch (error) {
          console.error('Error creating receipt:', error.response.data);
          setMessage('Failed to confirm receipt.');
          setIsError(true);
      }    
  
      setTimeout(() => {
        setMessage('');
        setIsError(false);
      }, 5000);
    }
  };

  return (
    <div className="p-4 w-full">
      {/* Tab Navigation */}
      <div className="tabs flex space-x-3 mb-4 font-semibold">
        {['receipt', 'category'].map(tab => (
          <button
            key={tab}
            className={`px-4 py-2 rounded-md ${activeTab === tab ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-blue-100'}`}
            onClick={() => handleTabChange(tab)}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)} Form
          </button>
        ))}
        {/* Refresh Icon */}
        <div className="p-1.5 rounded-full bg-gray-100 transition duration-200">
          <button
            className="p-1.5 rounded-md text-gray-700 hover:text-blue-600"
            onClick={() => window.location.reload()}
          >
            <FaSync className="text-base" />
          </button>
        </div>
      </div>
      <ReceiptProps
        activeTab={activeTab}
        loading={loading}
        setLoading={setLoading}
        receipt={currentReceipt}
        onConfirm={handleConfirm}
        errors={errors}
        setIsError={setIsError}
        message={message}
        setMessage={setMessage}
        isError={isError}
        grandTotal={grandTotal} // Pass the grand total
        setReceipt={(updatedReceipt) => {
          setCurrentReceipt(updatedReceipt);
        }}
      />
    </div>
  );
};
