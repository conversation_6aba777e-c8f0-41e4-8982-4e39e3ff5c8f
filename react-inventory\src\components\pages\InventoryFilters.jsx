import React, { useState, useRef, useEffect } from 'react';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const InventoryFilters = ({ categories, inventory, onFilterChange, filteredInventory, setFilteredInventory }) => {
  const [receiptId, setReceiptId] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [availability, setAvailability] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('');

  const filters = [
    { label: 'Receipt ID', value: 'receiptId' },
    { label: 'Date Range', value: 'dateRange' },
    { label: 'Category', value: 'category' },
    { label: 'Price Range', value: 'priceRange' },
    { label: 'Availability', value: 'availability' },
    { label: 'Search', value: 'search' },
  ];

  const dropdownRef = useRef(null);
  const activeFilterRef = useRef(null);

  const handleFilterChange = () => {
    console.log('handleFilterChange called');
    if (activeFilter === 'dateRange') {
      const filteredInventory = handleDateRangeFilter(new Date(startDate), new Date(endDate));
      onFilterChange(filteredInventory);
    } else if (activeFilter === 'receiptId') {
      const receiptIdNum = parseInt(receiptId);
      if (isNaN(receiptIdNum)) {
        // Display an error message or prevent the filter from being applied
        console.error('Invalid receipt ID. Please enter a number.');
        return;
      }
      const filteredInventory = inventory.filter((product) => product.receipt_id === receiptIdNum);
      if (filteredInventory.length === 0) {
        // Add a CSS class to the input field to change its border color to red
        const receiptIdInput = document.querySelector('input[type="text"][value="' + receiptId + '"]');
        if (receiptIdInput) {
          receiptIdInput.classList.add('border-red-500');
        }
      } else {
        // Remove the CSS class if the receipt ID exists in the database
        const receiptIdInput = document.querySelector('input[type="text"][value="' + receiptId + '"]');
        if (receiptIdInput) {
          receiptIdInput.classList.remove('border-red-500');
        }
      }
      onFilterChange(filteredInventory);
      } else if (activeFilter === 'category') {
        const filteredInventory = inventory.filter((product) => product.category_name === selectedCategory);
        onFilterChange(filteredInventory);
      } else if (activeFilter === 'priceRange') {
        const minPriceNum = parseFloat(minPrice);
        const maxPriceNum = parseFloat(maxPrice);
        const filteredInventory = inventory.filter((product) => product.price >= minPriceNum && product.price <= maxPriceNum);
        onFilterChange(filteredInventory);
      } else if (activeFilter === 'availability') {
        const filteredInventory = inventory.filter((product) => {
          if (product.qty_of_item === 0) {
            return availability === 'outOfStock';
          } else if (product.qty_of_item <= 10) {
            return availability === 'lowStock';
          } else {
            return availability === 'inStock';
          }
        });
        onFilterChange(filteredInventory);
      } else if (activeFilter === 'search') {
        const filteredInventory = inventory.filter((product) => product.item_name.includes(searchTerm));
        onFilterChange(filteredInventory);
      }
    };

  const handleDateRangeFilter = (startDate, endDate) => {
    const filteredInventory = inventory.filter((product) => {
      const productDate = new Date(product.date);
      return productDate >= startDate && productDate <= endDate;
    });
    return filteredInventory;
  };

  const handleReset = () => {
    setReceiptId('');
    setStartDate('');
    setEndDate('');
    setSelectedCategory('');
    setMinPrice('');
    setMaxPrice('');
    setAvailability('');
    setSearchTerm('');
    handleFilterChange();
    setFilteredInventory(inventory);
  };

  const handleFilterToggle = (filterValue) => {
    setActiveFilter(activeFilter === filterValue ? '' : filterValue);
    if (filterValue === 'sortBy') {
      setStartDate('');
      setEndDate('');
      setSelectedCategory('');
      setMinPrice('');
      setMaxPrice('');
      setAvailability('');
      setSearchTerm('');
    }
    if (activeFilter === '') {
      setFilteredInventory(inventory);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && !dropdownRef.current.contains(event.target) &&
        activeFilterRef.current && !activeFilterRef.current.contains(event.target)
      ) {
        setActiveFilter('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Check if any filter has a value
  const isFilterActive =
    activeFilter !== 'sortBy' && (receiptId || startDate || endDate || selectedCategory || minPrice || maxPrice || availability || searchTerm);

  // Determine if buttons should be visible
  const showButtons = activeFilter && isFilterActive;

  const handlePrintData = () => {
    const doc = new jsPDF();
    const data = filteredInventory.length > 0 ? filteredInventory : inventory;
  
    doc.text('Fligno Inventory Report', 10, 10);
  
    const tableData = data.map((item) => [
      item.date,
      item.category_name,
      item.item_name,
      item.qty_of_item,
      item.qty_of_pack,
      item.price,
      item.totalAmount,
      item.store,
      item.receipt_no,
      item.stock_status
    ]);
  
    doc.autoTable({
      head: [['Date', 'Category', 'Item Name', 'Quantity of Item', 'Quantity of Pack', 'Price', 'Total Amount', 'Store', 'Receipt Number', 'Stock Status']],
      body: tableData,
      didDrawCell: (data) => {
        if (data.column.index === 9) {
          if (data.cell.raw === 'In-stock') {
            doc.setTextColor(0, 128, 0);
          } else if (data.cell.raw === 'Low Stock') {
            doc.setTextColor(255, 165, 0);
          } else if (data.cell.raw === 'Out of Stock') {
            doc.setTextColor(255, 0, 0);
          }
        }
      },
      willDrawCell: (data) => {
        if (data.column.index === 9) {
          if (data.cell.raw === 'In-stock') {
            doc.setTextColor(0, 128, 0);
          } else if (data.cell.raw === 'Low Stock') {
            doc.setTextColor(255, 165, 0);
          } else if (data.cell.raw === 'Out of Stock') {
            doc.setTextColor(255, 0, 0);
          }
        }
      }
    });

    // Add a footer text at the bottom of the page
    const footerText = 'This serves as the initial inventory report.';
    const footerY = 270;
    
    doc.text(footerText, 62, footerY, null, null, 'center');
  
    const string = doc.output('bloburl');
    const iframe = document.createElement('iframe');
    iframe.src = string;
    iframe.style.position = 'fixed';
    iframe.onload = function() {
      iframe.contentWindow.print();
    };
    document.body.appendChild(iframe);
  };

  return (
    <div className="flex">
      {/* Filters on the left */}
      <div className="mr-4" ref={activeFilterRef}>
        {activeFilter && (
          <div className="border border-gray-300 rounded-md shadow-lg mr-4">
            {activeFilter === 'receiptId' && (
              <div className="p-3">
                <label className="block mb-1">Receipt ID:</label>
                <input
                  type="text"
                  value={receiptId}
                  onChange={(e) => setReceiptId(e.target.value)}
                  className={`border ${receiptId && !inventory.find((product) => product.receipt_id === parseInt(receiptId)) ? 'border-red-500' : 'border-gray-400'} p-1 w-full`}
                />
              </div>
            )}
            {activeFilter === 'dateRange' && (
              <div className="p-2 grid grid-cols-2 gap-4">
                <div>
                  <label className="block mb-1">Start Date:</label>
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className={`border ${startDate && !endDate || startDate > endDate ? 'border-red-500' : 'border-gray-400'} px-1 w-full mb-2`}
                  />
                </div>
                <div>
                  <label className="block mb-1">End Date:</label>
                  <input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className={`border ${startDate && !endDate || startDate > endDate ? 'border-red-500' : 'border-gray-400'} px-1 w-full`}
                  />
                </div>
                {startDate && !endDate || startDate > endDate && (
                  <div className="text-red-500 text-sm mt-1">
                    {startDate && !endDate ? 'Please select an end date' : 'End date should be after start date'}
                  </div>
                )}
              </div>
            )}

            {activeFilter === 'category' && (
              <div className="p-3">
                <label className="block mb-1">Category:</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="border border-gray-400 px-1 w-full"
                >
                  <option value="">Select Category</option>
                  {Array.isArray(categories) &&
                    categories.map((category, index) => (
                      <option key={index} value={category.category_name}>
                        {category.category_name}
                      </option>
                    ))}
                </select>
              </div>
            )}

            {activeFilter === 'priceRange' && (
              <div className="p-2 grid grid-cols-2 gap-4">
                <div>
                  <label className="block mb-1">Min Price:</label>
                  <input
                    type="number"
                    value={minPrice}
                    onChange={(e) => setMinPrice(e.target.value)}
                    className={`border ${minPrice > maxPrice ? 'border-red-500' : 'border-gray-400'} w-full mb-2`}
                  />
                </div>
                <div>
                  <label className="block mb-1">Max Price:</label>
                  <input
                    type="number"
                    value={maxPrice}
                    onChange={(e) => setMaxPrice(e.target.value)}
                    className={`border ${minPrice > maxPrice ? 'border-red-500' : 'border-gray-400'} w-full`}
                  />
                </div>
                {minPrice > maxPrice && (
                  <div className="text-red-500 text-sm mt-1"/>
                )}
              </div>
            )}

            {activeFilter === 'availability' && (
              <div className="p-3">
                <label className="block mb-1">Availability:</label>
                <select
                  value={availability}
                  onChange={(e) => setAvailability(e.target.value)}
                  className="border border-gray-400 p-1 w-full"
                >
                  <option value="">Select Availability</option>
                  <option value="inStock">In-stock</option>
                  <option value="lowStock">Low Stock</option>
                  <option value="outOfStock">Out of Stock</option>
                </select>
              </div>
            )}

            {activeFilter === 'search' && (
              <div className="p-3">
                <label className="block mb-1">Search Term:</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border border-gray-400 p-1 w-full"
                />
              </div>
            )}

            {/* Show buttons only when there's an active filter with a value */}
            {showButtons && (
              <div className="flex justify-end px-2 py-2">
                <button
                  onClick={handleFilterChange}
                  className="bg-blue-500 text-white px-4 py-2 rounded-md"
                >
                  Apply Filter
                </button>
                <button
                  onClick={handleReset}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md ml-2"
                >
                  Reset
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Sort By Dropdown */}
      <div className="flex-grow" ref={dropdownRef}>
        <div className="relative inline-block text-left">
          <button
            onClick={() => handleFilterToggle('sortBy')}
            className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100"
          >
            Sort By
          </button>
          {activeFilter === 'sortBy' && (
            <div className="absolute mt-2 w-48 bg-white border rounded-md shadow-lg">
              <ul>
                {filters.map((filter) => (
                  <li
                    key={filter.value}
                    onClick={() => handleFilterToggle(filter.value)}
                    className="px-4 py-2 hover:bg-blue-100 cursor-pointer"
                  >
                    {filter.label}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      <div className='ml-2'>
        <button onClick={handlePrintData} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100">
          Print Data
        </button>
      </div>
    </div>
  );
};

export default InventoryFilters;
