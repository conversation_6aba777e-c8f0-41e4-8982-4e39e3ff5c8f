import React, { useEffect, useState } from 'react';
import { BsArrowLeftShort } from "react-icons/bs";
import { DASHBOARD_SIDEBAR_LINKS, DASHBOARD_SIDEBAR_BOTTOM_LINKS, ADMIN_SIDEBAR_LINKS } from '../../lib/consts/navigation';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import classNames from 'classnames';
import { api } from '../../../api/apiService';

const linkClasses = "flex items-center gap-x-4 cursor-pointer px-2.5 py-2 hover:bg-light-white rounded-md mt-4 text-base font-medium";

export const Sidebar = ({ open, setOpen, isAdmin }) => {
  const navigate = useNavigate();
  const { pathname } = useLocation(); // Get the current path

  useEffect(() => {
    const storedOpenState = localStorage.getItem('sidebarOpen');
    if (storedOpenState !== null) {
      setOpen(storedOpenState === 'true');
    }
  }, [setOpen]);

  useEffect(() => {
    localStorage.setItem('sidebarOpen', open.toString());
  }, [open]);

  const handleLogout = async () => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      try {
        await api.post('/logout', {}, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        localStorage.removeItem('authToken');
        navigate('/login');
      } catch (error) {
        console.error("Error logging out:", error);
      }
    } else {
      navigate('/login');
    }
  };

  return (
    <div className={`bg-blue-950 p-5 pt-8 ${open ? 'w-90' : 'w-20'} duration-300 flex-col inline-flex`}>
      <div className='relative flex w-96 items-center px-0.5 py-1'>
        <BsArrowLeftShort 
          className={`bg-white text-blue-950 text-3xl rounded-full absolute top-2 border border-blue-950 cursor-pointer ${!open && 'rotate-180'} ${open ? 'right-2' : 'left-11'}`}
          onClick={() => setOpen(!open)} 
          aria-label="Toggle sidebar"
        />
        <img 
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s" 
          alt="Logo" 
          className={`rounded-full cursor-pointer block float-left mr-3 duration-500 ${!open && "rotate-[-360deg]"}`} 
          style={{ width: '40px', height: '40px' }}
        />
        <h1 className={`text-white origin-left font-medium duration-300 text-2xl ${!open && 'scale-0'}`}>
          Inventory Management
        </h1>
      </div>

      {/* General Dashboard Links */}
      <div className='flex-1 flex flex-col gap-0.5'>
        {DASHBOARD_SIDEBAR_LINKS.map((item) => (
          <SidebarLink key={item.key} item={item} open={open} pathname={pathname} />
        ))}
      </div>

      {/* Bottom Links */}
      <div className='flex flex-col gap-0.5 pt-2 border-t border-neutral-700'>
        {isAdmin ? (
            <SidebarLink item={ADMIN_SIDEBAR_LINKS[0]} open={open} pathname={pathname} />
        ) : null}
        {DASHBOARD_SIDEBAR_BOTTOM_LINKS.map((item) => (
          <SidebarLink key={item.key} item={item} open={open} pathname={pathname} onClick={(e) => {
            if (item.key === 'logout') {
              e.preventDefault();
              handleLogout();
            }
          }} />
        ))}
      </div>
    </div>
  );
};

function SidebarLink({ item, open, pathname, onClick }) {
  const [hovered, setHovered] = useState(false);

  return (
    <div className="relative group" 
         onMouseEnter={() => setHovered(true)} 
         onMouseLeave={() => setHovered(false)}>
      <Link
        to={item.path}
        className={classNames(
          pathname === item.path ? 'bg-light-white text-white' : 'text-slate-500',
          linkClasses
        )}
        aria-label={item.label}
        onClick={onClick}
      >
        <span className="text-xl">{item.icon}</span>
        {open && item.label}
      </Link>

      {/* Tooltip for closed sidebar */}
      {!open && hovered && ( // Show tooltip only if hovered
        <span className="absolute left-full top-2/3 transform -translate-y-1/2 ml-2 w-auto whitespace-nowrap px-2 py-1 rounded-md bg-gray-700 text-white text-base opacity-100 duration-300">
          {item.label}
        </span>
      )}
    </div>
  );
}
