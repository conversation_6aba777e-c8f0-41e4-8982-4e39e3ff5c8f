<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User; 
use Illuminate\Support\Facades\Hash;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        // Validate the request
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);
    
        // Check if the user exists by email
        $user = User::where('email', $request->email)->first();
    
        // If the user does not exist
        if (!$user) {
            return response()->json(['message' => 'This email is not registered.'], 404);
        }
    
        // Check if the password is incorrect
        if (!Hash::check($request->password, $user->password)) {
            return response()->json(['message' => 'The password is incorrect.'], 401);
        }

        // Revoke any existing tokens
        $user->tokens->each(function ($token) {
            $token->revoke();
        });
    
        // Generate a token
        $token = $user->createToken('authToken')->accessToken;
    
        // Prepare the response data
        $response = [
            'token' => $token,
            'userStatus' => $user->status,
            'isAdmin' => (bool)$user->is_admin,
            'message' => 'Login successful.',
        ];
    
        // Optionally, create an admin token if the user is an admin
        if ($user->is_admin) {
            $adminToken = $user->createToken('adminToken')->accessToken;
            $response['adminToken'] = $adminToken; // Include admin token in response
        }
    
        return response()->json($response);
    }    

    
    public function getUserData(Request $request)
    {
        return response()->json($request->user());
    }

    public function getUserRole(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        return response()->json(['is_admin' => (bool)$user->is_admin]);
    }

    public function userStatus(Request $request)
    {
        $user = $request->user();
        return response()->json(['status' => $user->status]);
    }
}
