<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle($request, Closure $next)
    {
        // Check if the user is authenticated and is an admin
        if (!Auth::check()) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        if (!Auth::user()->is_admin) {
            return response()->json(['message' => 'Unauthorized. You do not have permission to perform this action.'], 403);
        }

        return $next($request);
    }
}
