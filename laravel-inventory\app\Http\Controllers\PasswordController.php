<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Token;

class PasswordController extends Controller
{
    
public function setPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => [
                'required',
                'min:8',
                'regex:/[A-Z]/',
                'regex:/[@$!%*#?&]/'
            ],
            'verification_code' => 'required|numeric|digits:6'
        ], [
            'password.regex' => 'The password must contain at least one uppercase letter and one special character.',
            'verification_code.required' => 'Verification code is required.',
            'verification_code.digits' => 'Verification code must be exactly 6 digits.'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 400);
        }

        $user = User::where('email', $request->input('email'))->first();

        if (!$user) {
            return response()->json([
                'message' => 'User does not exist.'
            ], 404);
        }

        if ($user->verification_code !== $request->input('verification_code')) {
            return response()->json(['message' => 'Invalid verification code.'], 403);
        }

        if ($user->password) {
            // Revoke existing tokens
            $user->tokens->each(function ($token) {
                $token->delete();
            });

            \Log::info('User already has a password:', ['user_id' => $user->id]);
            return response()->json([
                'message' => 'Password is already set. Please log in.'
            ], 403);
        }

        $user->password = Hash::make($request->input('password'));
        $user->email_verified_at = now();
        $user->status = 'pending'; // Update the status to inactive once the user sets up a password
        $user->save();

        return response()->json(['message' => 'Password set successfully.']);
    }
}
