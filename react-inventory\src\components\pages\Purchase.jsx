/* import React, { useState } from 'react';
import { MdRemoveCircle } from 'react-icons/md';
import { FaArrowLeft } from 'react-icons/fa';
import PurchaseProps from '../pages/PurchaseProps';
import { api } from '../../api/apiService';

export const Purchase = () => {
  const today = new Date().toISOString().split('T')[0];
  const [listedDate, setListedDate] = useState(today);
  const [purchaseDetails, setPurchaseDetails] = useState([]);
  const [message, setMessage] = useState('');
  const [isError, setIsError] = useState(false);
  const [isAddingItems, setIsAddingItems] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const handleConfirmPurchase = async () => {
    if (purchaseDetails.length === 0) {
      setMessage('No purchases to confirm.');
      setIsError(true);
      return;
    }

    try {
      const response = await api.post('/purchases', {
        purchases: purchaseDetails.map((purchase) => ({
          listed_date: listedDate,
          item_name: purchase.item_name,
          qty_of_item: purchase.qty_of_item,
          qty_of_pack: purchase.qty_of_pack,
        })),
      });

      if (response.status === 200) {
        setMessage('Purchases confirmed successfully.');
        setIsError(false);
        setPurchaseDetails([]); // Clear the purchase list
        setCurrentPage(1); // Reset page
      } else {
        setMessage(`Error: ${response.data.message || 'Something went wrong'}`);
        setIsError(true);
      }
    } catch (error) {
      console.log('purchaseDetails:', purchaseDetails);
      console.error('Error confirming purchases:', error);
      setMessage('Failed to confirm purchases.');
      setIsError(true);
    }

    // Clear the message after 5 seconds
    setTimeout(() => {
      setMessage('');
    }, 5000);
  };

  const handleDateSubmit = (e) => {
    e.preventDefault();
    setIsAddingItems(true);
  };

  const handleAddPurchase = (newPurchase) => {
    setPurchaseDetails([...purchaseDetails, newPurchase]);
    setMessage('Purchase item added.');

    // Clear message after 5 seconds
    setTimeout(() => {
      setMessage('');
    }, 5000);
  };

  const handleRemovePurchase = (index) => {
    setPurchaseDetails(purchaseDetails.filter((_, i) => i !== index));
    setMessage('Purchase item removed.');
    setIsError(true);

    // Clear message after 5 seconds
    setTimeout(() => {
      setMessage('');
    }, 5000);
  };

  // Pagination logic
  const totalPages = Math.ceil(purchaseDetails.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = purchaseDetails.slice(indexOfFirstItem, indexOfLastItem);

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <div className='p-4 w-full'>
      <div className="p-6 bg-white rounded-lg shadow-md">
        {message && (
          <div className={`p-2 mb-4 text-center ${isError ? 'bg-red-200 text-red-600' : 'bg-green-200 text-green-600'}`}>
            {message}
          </div>
        )}
        {!isAddingItems ? (
          <form onSubmit={handleDateSubmit}>
            <h1 className="text-2xl text-center font-bold mb-4">Select Purchase Date</h1>
            <div className="flex justify-center">
              <input
                type="date"
                value={listedDate}
                onChange={(e) => setListedDate(e.target.value)}
                className="mt-1 text-center block w-1/3 border border-gray-400 rounded-lg p-2"
                min={today}
                required
              />
            </div>
            <div className="flex justify-center mt-4">
              <button type="submit" className="w-1/4 flex items-center justify-center bg-blue-500 text-white rounded-lg py-2 hover:bg-blue-600">
                Confirm Date
              </button>
            </div>
          </form>
        ) : (
          <>
            <button
              className="bg-gray-200 text-gray-700 px-2 py-2 rounded-md mb-2"
              onClick={() => setIsAddingItems(false)}
            >
              <FaArrowLeft />
            </button>
            <h2 className="text-xl text-center font-bold mb-2">Add Purchases for {listedDate}</h2>
            <div className="flex">
              <div className="w-1/2 pr-2">
                <PurchaseProps listedDate={listedDate} onAddPurchase={handleAddPurchase} />
              </div>
              <div className="w-1/2 pl-2">
                <table className="min-w-full border text-center">
                  <thead>
                    <tr className="bg-gray-300">
                      <th className="border border-gray-400 px-4 py-2">Item Name</th>
                      <th className="border border-gray-400 px-4 py-2">Quantity of Item</th>
                      <th className="border border-gray-400 px-4 py-2">Quantity of Pack</th>
                      <th className="border border-gray-400 px-4 py-2">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentItems.length === 0 ? (
                      <tr>
                        <td colSpan="4" className="border px-4 py-6 text-center border-gray-400 text-gray-500">
                          You don't have any products added yet.
                        </td>
                      </tr>
                    ) : (
                      currentItems.map((purchase, index) => (
                        <tr key={index}>
                          <td className="border px-4 py-2">{purchase.item_name}</td>
                          <td className="border px-4 py-2">{purchase.qty_of_item}</td>
                          <td className="border px-4 py-2">{purchase.qty_of_pack}</td>
                          <td className="border px-4 py-2 flex items-center justify-center">
                            <MdRemoveCircle
                              className="text-red-500 cursor-pointer hover:text-red-700"
                              onClick={() => handleRemovePurchase(index + indexOfFirstItem)} // Corrected index
                            />
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
                <div className="flex justify-between items-center mt-2">
                  <button
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md"
                    onClick={handlePrevious}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </button>
                  <span>
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md"
                    onClick={handleNext}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </button>
                </div>
                {purchaseDetails.length > 0 && (
                  <button onClick={handleConfirmPurchase} className="mt-2 w-full bg-green-500 text-white rounded-lg py-2 hover:bg-green-600">
                    Confirm All Purchases
                  </button>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
 */