import React, { useState, useEffect } from 'react';
import {
  FaCalendarAlt,
  FaExclamationCircle,
  FaPlus,
  FaTrash, // Icon for removing items
} from 'react-icons/fa';
import { MdCategory } from 'react-icons/md';
import { api } from '../../api/apiService';
 
const ReceiptProps = ({
  activeTab,
  loading,
  setLoading,
  receipt = {},
  onConfirm,
  errors,
  setIsError,
  message,
  setMessage,
  isError,
  grandTotal,
  setReceipt,
}) => {
  const [showInputs, setShowInputs] = useState(!!receipt.date);
  const [items, setItems] = useState(receipt.items || [{}]);
  const [categories, setCategories] = useState([]);
  const [newCategory, setNewCategory] = useState('');
  

  useEffect(() => {
    api.get('/category')
      .then(response => {
        setCategories(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.error(error);
      });
  }, []);

  
  const handleCreateCategory = () => {
    if (newCategory.trim()) {
      const existingCategory = categories.find(
        (category) => category.category_name.toLowerCase() === newCategory.toLowerCase()
      );
  
      if (existingCategory) {
        setMessage('Category already exists.');
        setIsError(true);
        setTimeout(() => {
          setMessage('');
          setIsError(false);
        }, 5000);
      } else {
        api.post('/category/create', { category_name: newCategory })
          .then((response) => {
            const newCategoryId = response.data.id;
            const updatedCategories = [
              ...categories,
              { id: newCategoryId, category_name: newCategory },
            ];
            setCategories(updatedCategories);
            setNewCategory('');
            setMessage('Category added successfully.');
            setIsError(false);
            setTimeout(() => {
              setMessage('');
              setIsError(false);
            }, 5000);
          })
          .catch((error) => {
            console.error(error);
            setMessage('Failed to add category.');
            setIsError(true);
            setTimeout(() => {
              setMessage('');
              setIsError(false);
            }, 5000);
          });
      }
    }
  };

  const handleDeleteCategory = (category) => {
    if (category.id) {
      api.delete(`/category/delete/${category.id}`)
        .then((response) => {
          // Fetch the latest categories from the API
          api.get('/category')
            .then(response => {
              setCategories(response.data); // Update the categories state
              setMessage('Category deleted successfully.'); // Set the message
              setIsError(true); // Reset the error state
              setTimeout(() => {
                setMessage(''); // Clear the message after 5 seconds
              }, 5000);
            })
            .catch(error => {
              console.error(error);
              setMessage('Failed to delete category.');
              setIsError(true);
              setTimeout(() => {
                setMessage('');
                setIsError(false);
              }, 5000);
            });
        })
        .catch((error) => {
          console.error(error);
          setMessage('Failed to delete category.');
          setIsError(true);
          setTimeout(() => {
            setMessage('');
            setIsError(false);
          }, 5000);
        });
    } else {
      api.get('/category')
        .then(response => {
          setCategories(response.data); // Update the categories state
          setMessage('Category deleted successfully.'); // Set the message
          setIsError(true); // Reset the error state
          setTimeout(() => {
            setMessage(''); // Clear the message after 5 seconds
          }, 5000);
        })
        .catch(error => {
          console.error(error);
          setMessage('Failed to delete category.');
          setIsError(true);
          setTimeout(() => {
            setMessage('');
            setIsError(false);
          }, 5000);
        });
    }
  };

  const handleDateChange = (e) => {
    const updatedReceipt = { ...receipt, date: e.target.value };
    setReceipt(updatedReceipt);

    // Show item input fields only when a date is selected
    if (e.target.value) {
      setShowInputs(true);
    } else {
      setShowInputs(false);
      setItems([{}]); // Reset items when date is cleared
    }
  };

  const handleItemChange = (index, field, value) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setItems(updatedItems);
    
    // Update the receipt state with the new items
    setReceipt({ ...receipt, items: updatedItems });
  };

  // Function to add a new item row
  const addItemRow = () => {
    const updatedItems = [...items, {}]; // Add a new empty object for the new item
    setItems(updatedItems);
    
    // Update the receipt state with the new items
    setReceipt({ ...receipt, items: updatedItems });
  };

  const removeItemRow = (index) => {
    const updatedItems = items.filter((_, i) => i !== index);
    setItems(updatedItems);
    
    // Update the receipt state with the new items
    setReceipt({ ...receipt, items: updatedItems });
  };

  const renderReceipt = () => (
    <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-6">
        {message && (
          <div
            className={`p-2 mb-4 text-center ${
              isError ? 'bg-red-200 text-red-600' : 'bg-green-200 text-green-600'
            }`}
          >
            {message}
          </div>
        )}
        <h2 className="text-2xl font-bold mb-4">Receipt Details</h2>
        <hr className="my-4 border-gray-300" />

        {/* Editable Receipt Date */}
        <div className="mb-5">
          <FaCalendarAlt className="inline mr-2 text-gray-500" />
          <strong>Receipt Date:</strong>
          <input
            type="date"
            value={receipt.date || ''}
            onChange={handleDateChange}
            className={`ml-2 py-0.5 text-center border border-gray-400 rounded ${
              errors['date'] ? 'border-red-500' : ''
            }`}
          />
        </div>

        {/* Show message if date is not selected */}
        {!showInputs && (
          <div className="bg-yellow-100 p-3 text-yellow-700 rounded-md mb-4 flex items-center">
            <FaExclamationCircle className="mr-2" />
            <span>Please select a date to add item details.</span>
          </div>
        )}

        {/* Input fields for items, shown only if a date is selected */}
        {showInputs && (
          <>
            <table className="min-w-full border border-gray-300 mb-4">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">Category</th>
                  <th className="border px-4 py-2">Item Name</th>
                  <th className="border px-4 py-2">Quantity of Item</th>
                  <th className="border px-4 py-2">Quantity of Pack</th>
                  <th className="border px-4 py-2">Price</th>
                  <th className="border px-4 py-2">Total Amount</th>
                  <th className="border px-4 py-2">Store</th>
                  <th className="border px-4 py-2">Receipt Number</th>
                  <th className="border px-4 py-2">Action</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item, index) => (
                  <tr key={index}>
                    <td className="border px-4 py-4">
                      <select
                        value={item.category_name || ''}
                        onChange={(e) => handleItemChange(index, 'category_name', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors[`category_name_${index}`] ? 'border-red-500' : ''}`}
                      >
                        <option value="">Select</option>
                        {categories.length > 0 ? (
                          categories.map((category, index) => (
                            <option key={index} value={category.category_name}>
                              {category.category_name}
                            </option>
                          ))
                        ) : (
                          <option disabled>Create a category</option>
                        )}
                      </select>
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="text"
                        value={item.item_name || ''}
                        onChange={(e) => handleItemChange(index, 'item_name', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['item_name'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="number"
                        value={item.qty_of_item || ''}
                        onChange={(e) => handleItemChange(index, 'qty_of_item', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['qty_of_item'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="number"
                        value={item.qty_of_pack || ''}
                        onChange={(e) => handleItemChange(index, 'qty_of_pack', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['qty_of_pack'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="number"
                        value={item.price || ''}
                        onChange={(e) => handleItemChange(index, 'price', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['price'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="number"
                        value={item.totalAmount || ''}
                        onChange={(e) => handleItemChange(index, 'totalAmount', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['totalAmount'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="text"
                        value={item.store || ''}
                        onChange={(e) => handleItemChange(index, 'store', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['store'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4">
                      <input
                        type="text"
                        value={item.receipt_no || ''}
                        onChange={(e) => handleItemChange(index, 'receipt_no', e.target.value)}
                        className={`py-1 text-center border border-gray-400 rounded w-full ${errors['receipt_no'] ? 'border-red-500' : ''}`}
                      />
                    </td>
                    <td className="border px-4 py-4 text-center">
                      <button
                        onClick={() => removeItemRow(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Display the grand total */}
            <div>
              <h3 className="text-lg font-semibold">Grand Total: ₱{grandTotal % 1 === 0 ? grandTotal : grandTotal.toFixed(2)}</h3>
            </div>
            <div className='flex justify-between'>
              <div className='mt-2'>
                <button
                  onClick={addItemRow}
                  className="bg-blue-500 text-white px-4 py-2 rounded mb-4 flex items-center"
                >
                  <FaPlus className="mr-2" />
                  Add Another Item
                </button>
              </div>

              {/* Confirm Button - only visible when date is selected */}
              <div className='mt-2'>
                <button
                  onClick={onConfirm}
                  className="bg-green-500 text-white px-4 py-2 rounded"
                >
                  Confirm Receipt
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    )

    const renderCategory = () => (
      <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-4">
        <h2 className="text-2xl font-bold mb-4">Add Category</h2>
        <hr className="my-4 border-gray-300" />
        <div className="flex">
          <input
            type="text"
            value={newCategory}
            onChange={(e) => setNewCategory(e.target.value)}
            placeholder="Add a new category"
            className="border border-gray-400 rounded p-2 flex-1 mr-2"
          />
          <button
            onClick={handleCreateCategory}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            <FaPlus />
          </button>
        </div>
        {categories.length === 0 ? (
          <p className="text-gray-500 text-lg text-center mt-4">
            No categories added yet. <FaExclamationCircle className="inline mr-2" />
          </p>
        ) : (
          <ul className="list-none mt-4">
            {categories.map((category, index) => (
              <li key={category.category_name} className="flex justify-between ml-2 text-lg border-b border-gray-300 mb-2">
                <span>
                  {index + 1}. {category.category_name}
                </span>
                <button
                  onClick={() => handleDeleteCategory(category)}
                  className="text-red-600 hover:text-red-800"
                >
                  <FaTrash />
                </button>
              </li>
            ))}
          </ul>
        )}
        {message && (
          <div
            className={`p-2 mt-4 text-center ${
              isError ? 'bg-red-200 text-red-600' : 'bg-green-200 text-green-600'
            }`}
          >
            {message}
          </div>
        )}
      </div>
    );

  return (
    <div className="w-full">
      {loading ? (
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
          <div className="flex justify-center">
            <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="none" stroke="currentColor" strokeWidth="4" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading inventory...</span>
          </div>
        </div>
      ) : (
        <div>
          {activeTab === 'receipt' && renderReceipt()}
          {activeTab === 'category' && renderCategory()}
        </div>
      )}
    </div>
  );
};

export default ReceiptProps;
