<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class DashboardController extends Controller
{
    public function index(Request $request, $id)
    {
        // Check if the user exists
        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'message' => 'User not found.'
            ], 404);
        }

        // Authenticate the user
        $authenticatedUser = Auth::user();

        if (!$authenticatedUser) {
            return response()->json([
                'message' => 'User not authenticated.'
            ], 401);
        }

        if ($authenticatedUser->id != $user->id) {
            return response()->json(['message' => 'Unauthorized access.'], 403);
        }

        if ($user->status !== 'active') {
            return response()->json([
                'message' => 'Your account is currently waiting for approval.'
            ], 403);
        }

        $isAdmin = $user->is_admin;

        return response()->json([
            'message' => 'Welcome to the dashboard.',
            'isAdmin' => $isAdmin
        ]);
    }
}
