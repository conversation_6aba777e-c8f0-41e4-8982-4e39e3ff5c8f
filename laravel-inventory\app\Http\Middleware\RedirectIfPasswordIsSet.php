<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RedirectIfPasswordIsSet
{
    public function handle($request, Closure $next)
{
    if (Auth::check() && Auth::user()->password && $request->route()->getActionMethod() !== 'verify' && $request->route()->getActionMethod() !== 'setPassword') {
        return redirect('/login');
    }

    return $next($request);
}
}
