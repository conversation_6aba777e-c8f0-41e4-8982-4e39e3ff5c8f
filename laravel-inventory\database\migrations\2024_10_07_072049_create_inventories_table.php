<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInventoriesTable extends Migration
{
    public function up()
    {
        Schema::create('inventories', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('receipt_id');
            $table->date('date');
            $table->string('category_name');
            $table->string('item_name');
            $table->integer('qty_of_item');
            $table->integer('qty_of_pack');
            $table->decimal('price', 10, 2);
            $table->decimal('totalAmount', 10, 2);
            $table->string('store');
            $table->string('receipt_no');
            $table->string('stock_status')->default('');
            $table->softDeletes();
            $table->timestamps();
        
            $table->foreign('receipt_id')->references('id')->on('receipts')->onDelete('cascade');
        });        
    }

    public function down()
    {
        Schema::dropIfExists('inventories');
    }
}
