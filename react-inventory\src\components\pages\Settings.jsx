import React, { useEffect, useState } from 'react'
import SettingsProps from '../pages/SettingsProps'
import { useNavigate } from 'react-router-dom'
import { v4 as uuidv4 } from 'uuid'
import { FaSync } from 'react-icons/fa'
import { api } from '../../api/apiService'

export const Settings = () => {
    const [users, setUsers] = useState([])
    const [authorizedPersonnel, setAuthorizedPersonnel] = useState([])
    const [isAuthorized, setIsAuthorized] = useState(false)
    const [activeTab, setActiveTab] = useState('users')
    const [errorMessages, setErrorMessages] = useState({})
    const navigate = useNavigate()

    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const response = await api.get('/settings/users')
                setUsers(response.data)
                setIsAuthorized(true)
            } catch (error) {
                if (error.response && error.response.status === 403) {
                    navigate('/')
                } else {
                    console.error('Error fetching users:', error.message)
                }
            }
        }

        const fetchAuthorizedPersonnel = async () => {
            try {
                const response = await api.get('/settings/authorized-personnel')
                setAuthorizedPersonnel(response.data)
            } catch (error) {
                console.error('Error fetching authorized personnel:', error.message)
            }
        }

        fetchUsers()
        fetchAuthorizedPersonnel()
    }, [navigate])

    const toggleUserStatus = (id) => {
        api.post(`/settings/toggle-status/${id}`)
            .then(response => {
                setUsers(prevUsers =>
                    prevUsers.map(user =>
                        user.id === id ? { ...user, status: response.data.status } : user
                    )
                )
                // Clear error message for this user if status changes to active
                setErrorMessages(prev => ({ ...prev, [id]: '' }))
            })
            .catch(error => {
                if (error.response && error.response.status === 400) {
                    // Set error message for inactive users
                    setErrorMessages(prev => ({
                        ...prev,
                        [id]: 'This user has not completed their registration and cannot be activated.'
                    }))
                } else {
                    console.error('Error updating user status:', error.message)
                }
            })
    }

    const addAuthorizedPersonnel = (email) => {
        const tempId = uuidv4() // Generate a temporary ID
        setAuthorizedPersonnel([...authorizedPersonnel, { email, id: tempId }]) // Immediately add to state
    
        api.post('/settings/add-authorized-personnel', { email })
            .then(response => {
                // Update the ID of the newly added authorized personnel from the response
                setAuthorizedPersonnel(prevAuthorizedPersonnel =>
                    prevAuthorizedPersonnel.map(person =>
                        person.id === tempId ? { ...person, id: response.data.id } : person
                    )
                )
            })
            .catch(error => {
                console.error('Error adding authorized personnel:', error.message)
                // Optionally, you can also remove the temporarily added person if the API call fails
                setAuthorizedPersonnel(prevAuthorizedPersonnel =>
                    prevAuthorizedPersonnel.filter(person => person.id !== tempId)
                )
            })
    }
    
    const removeAuthorizedPersonnel = (id) => {
        // Optimistically update the UI first
        setAuthorizedPersonnel(authorizedPersonnel.filter(person => person.id !== id))
    
        api.post(`/settings/remove-authorized-personnel/${id}`)
            .then(() => {
                // Optionally, do nothing here since the state is already updated
            })
            .catch(error => {
                console.error('Error removing authorized personnel:', error.message)
                // If the removal fails, you can re-add the user back to the state
                const personToRestore = authorizedPersonnel.find(person => person.id === id)
                if (personToRestore) {
                    setAuthorizedPersonnel(prevAuthorizedPersonnel => [...prevAuthorizedPersonnel, personToRestore])
                }
            })
    }    

    if (!isAuthorized) {
        return null // Do not render if not authorized
    }

    return (
        <div className="p-4 w-full">
            {/* Navigation Tabs */}
            <div className="tabs flex space-x-4 mb-4">
                <button
                    onClick={() => setActiveTab('users')}
                    className={`p-2 px-4 rounded-md font-semibold transition duration-200 ${
                        activeTab === 'users' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-blue-100'
                    }`}
                >
                    Manage Registered Users
                </button>
                <button
                    onClick={() => setActiveTab('authorized')}
                    className={`p-2 px-4 rounded-md font-semibold transition duration-200 ${
                        activeTab === 'authorized' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-blue-100'
                    }`}
                >
                    Manage Authorized Users
                </button>
                {/* Refresh Icon */}
                <div className="p-1.5 rounded-full bg-gray-100 transition duration-200">
                    <button
                        className="p-1.5 rounded-md text-gray-700 hover:text-blue-600"
                        onClick={() => window.location.reload()}
                    >
                        <FaSync className="text-base" /> {/* Adjusted icon size */}
                    </button>
                </div>
            </div>

            {/* Content based on active tab */}
            <SettingsProps
                users={users}
                toggleUserStatus={toggleUserStatus}
                authorizedPersonnel={authorizedPersonnel}
                addAuthorizedPersonnel={addAuthorizedPersonnel}
                removeAuthorizedPersonnel={removeAuthorizedPersonnel}
                errorMessages={errorMessages}
                activeTab={activeTab}
            />
        </div>
    )
}
