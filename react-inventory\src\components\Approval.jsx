import React, { useEffect, useState } from 'react';
import { HiCheckBadge } from 'react-icons/hi2';
import { useNavigate } from 'react-router-dom';
import { api } from '../api/apiService';

export const Approval = () => {
  const navigate = useNavigate();
  const [approvalStatus, setApprovalStatus] = useState('pending');
  const [error, setError] = useState('');
  const [userExists, setUserExists] = useState(true);

  useEffect(() => {
    const authToken = localStorage.getItem('authToken');

    // If there's no token, navigate to login
    if (!authToken) {
      navigate('/login');
      return;
    }

    // Fetch approval status with token
    const fetchApprovalStatus = async () => {
      try {
        const response = await api.get('/user-status', {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        setApprovalStatus(response.data.status);

        // Redirect active users here instead of in Layout
        if (response.data.status === 'active') {
          navigate('/');
        }
      } catch (error) {
        console.error("Error fetching approval status:", error);
        setError('Failed to fetch approval status.');
        navigate('/login');
      }
    };

    // Fetch user data to check if user exists
    const fetchUserData = async () => {
      try {
        const response = await api.get('/user-data', {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        setUserExists(true);
      } catch (error) {
        console.error("Error fetching user data:", error);
        if (error.response.status === 404) {
          setUserExists(false);
        }
      }
    };

    fetchApprovalStatus();
    fetchUserData();
  }, [navigate]);

if (!userExists) {
  localStorage.removeItem('authToken');
  navigate('/login');
}

  const handleLogout = async () => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      try {
        await api.post('/logout', {}, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        localStorage.removeItem('authToken');
        navigate('/login');
      } catch (error) {
        console.error("Error logging out:", error);
        setError('Failed to log out.');
      }
    } else {
      navigate('/login');
    }
  };

  return (
    <section className="min-h-screen flex flex-col">
      <header className="flex justify-between items-center p-4 bg-gray-800 text-white">
        <h1 className="text-xl font-bold">Account Approval</h1>
        <button onClick={handleLogout} className="text-white bg-red-600 px-4 py-2 rounded hover:bg-red-700">
          Logout
        </button>
      </header>
      <div className="flex flex-col md:flex-row flex-grow">
        <div className="flex items-center justify-center bg-blue-950 p-8 md:w-1/2">
          <div className="text-center">
            <img 
              src='https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s' 
              alt='Company Logo' 
              className="w-32 h-32 md:w-48 md:h-48 object-contain mx-auto mb-6"
            />
            <h3 className="text-4xl text-white font-bold">Welcome to Fligno Inventory Management</h3>
          </div>
        </div>
        <div className="flex items-center justify-center bg-blue-500 text-center p-6 md:w-1/2">
          <div className="max-w-lg mx-auto p-10 bg-slate-50 rounded-lg shadow-xl">
            <HiCheckBadge className="w-16 h-16 mx-auto text-green-500 mb-4" />
            <h2 className="text-2xl font-bold mb-4">Thank you for registering with us!</h2>
            <p className="text-sm text-gray-700 mb-4">
              We're excited to have you onboard. Your account is 
              currently <strong>{approvalStatus}</strong> approval from our 
              admin team to ensure everything is in order. 
              This process may take a little time, but we will 
              notify you as soon as your account is activated. We 
              appreciate your patience and look forward to serving you soon! 
            </p>
            {error && <p className="text-red-500">{error}</p>}
          </div>
        </div>
      </div>
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </section>
  )
}
