import React from 'react';
import { Link } from 'react-router-dom';

export const TermsOfUse = () => {
  return (
    <div className="relative min-h-screen bg-gray-100">
      <header className="p-2 text-gray-800 shadow-md bg-blue-950">
        <div className="max-w-screen-xl mx-auto flex items-center justify-between">
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s"
            alt="Company Logo"
            className="w-16 h-16 object-contain"
          />
          <nav className="flex space-x-4 mr-5">
            <Link to="/login" className="text-white hover:text-gray-300 transition duration-300">Login</Link>
            <Link to="/register" className="text-white hover:text-gray-300 transition duration-300">Register</Link>
          </nav>
        </div>
      </header>

      <div className="flex items-center justify-center min-h-[calc(100vh-136px)] py-16 bg-sky-700">
        <div className="text-left p-12 bg-white rounded-lg shadow-md max-w-2xl mx-auto">
          <h2 className="text-3xl font-bold mb-4">Terms of Use</h2>
          <p className="text-gray-800 mb-4">
            Welcome to Fligno Inventory Management. By accessing or using our services, you agree to be bound by these terms.
          </p>
          
          <h3 className="text-xl font-bold mb-2">1. User Obligations</h3>
          <p className="text-gray-800 mb-4">
            Users must provide accurate information, maintain the confidentiality of their account, and are responsible for all activities under their account.
          </p>

          <h3 className="text-xl font-bold mb-2">2. Prohibited Activities</h3>
          <p className="text-gray-800 mb-4">
            Users are prohibited from using the system for illegal activities, sharing unauthorized data, or attempting to gain unauthorized access to the system.
          </p>

          <h3 className="text-xl font-bold mb-2">3. Data Disclosure and Sharing</h3>
          <p className="text-gray-800 mb-4">
            Data within the system is confidential and only accessible by authorized users. Unauthorized sharing of data will lead to immediate termination and possible legal actions.
          </p>

          <h3 className="text-xl font-bold mb-2">4. Consequences of Violations</h3>
          <p className="text-gray-800 mb-4">
            Violating these terms may result in account suspension, legal action, or other consequences as deemed appropriate. Any misuse will be dealt with accordingly.
          </p>

          <h3 className="text-xl font-bold mb-2">5. Modifications to Terms</h3>
          <p className="text-gray-800 mb-4">
            We reserve the right to update these terms at any time. Changes will be communicated to users via the platform or email.
          </p>

          <h3 className="text-xl font-bold mb-2">7. Contact Us</h3>
          <p className="text-gray-800">
            For any questions regarding this policy, please contact us at <a href="mailto:<EMAIL>" className="text-blue-500 hover:text-blue-700"><EMAIL></a>.
          </p>
        </div>
      </div>

      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
