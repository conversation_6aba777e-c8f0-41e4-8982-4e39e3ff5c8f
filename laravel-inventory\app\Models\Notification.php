<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    // Define the fillable properties
    protected $fillable = [
        'message',
        'is_read',
    ];

    // Optionally, define the casts for your fields
    protected $casts = [
        'is_read' => 'boolean',
    ];
}
