import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { BsArrowLeftShort } from 'react-icons/bs';
import { MdError } from 'react-icons/md';
import { HiEye, HiEyeSlash } from 'react-icons/hi2';
import { api } from '../../api/apiService';

export const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const validateEmail = (email) => /\S+@\S+\.\S+/.test(email);

  useEffect(() => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      navigate('/');
    }
  }, [navigate]);
  
  const validatePassword = (password) => {
    if (password.length < 8) return 'Password must be at least 8 characters long.';
    if (!/[A-Z]/.test(password)) return 'Password must contain at least one uppercase letter.';
    if (!/[!@#$%^&*]/.test(password)) return 'Password must contain at least one special character.';
    return '';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setEmailError('');
    setPasswordError('');

    const isEmailValid = validateEmail(email);
    const passwordErrorMessage = validatePassword(password);

    if (!isEmailValid || passwordErrorMessage) {
      setError('Invalid email or password.');
      setEmailError(isEmailValid ? '' : 'Invalid email.');
      setPasswordError(passwordErrorMessage);
      return;
    }

    setLoading(true);

    try {
      const response = await api.post('/login', { email, password });
      const { token, userStatus, isAdmin } = response.data;

      localStorage.setItem('authToken', token);

      if (userStatus === 'active') {
        navigate(isAdmin ? '/' : '/'); // Redirect to dashboard
      } else {
        navigate('/'); // Redirect to approval page
      }
    } catch (err) {
      handleErrorResponse(err);
    } finally {
      setLoading(false);
    }
  };

  const handleErrorResponse = (err) => {
    const { status, data } = err.response || {};

    if (status === 404 && data?.message === 'This email is not registered.') {
      setError('Email is not registered.');
      setEmailError('Invalid email.');
    } else if (status === 401) {
      if (data?.message === 'The password is incorrect.') {
        setError('Incorrect password.');
        setPasswordError('Invalid password.');
      } else {
        setError('Invalid email or password.');
        setEmailError('Invalid email.');
        setPasswordError('Invalid password.');
      }
    } else {
      setError('An unexpected error occurred.');
      setEmailError('Invalid email.');
      setPasswordError('Invalid password.');
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-blue-950">
      <div className="flex-grow flex items-center justify-center">
        <div className="relative w-[540px] h-[600px] bg-neutral-100 rounded-lg shadow-md">
          <div className="absolute inset-0 px-[70px] py-[48px] flex flex-col justify-between">
            <div className="text-center mb-6">
              <img 
                src='https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s' 
                alt='company_logo' 
                className="mx-auto mb-4 w-20 h-20 object-contain"
              />
              <h2 className="text-2xl font-bold">Log in to your account</h2>
            </div>
            <form onSubmit={handleSubmit} className="flex flex-col h-full">
              {/* Email Input */}
              <div className="relative mb-4 group">
                <label className="absolute -top-3 left-3 px-1 text-sm font-medium text-gray-700 bg-neutral-100 z-10 group-focus-within:text-blue-600" htmlFor="email">Email</label>
                <input 
                  type="email"
                  id="email"
                  autoComplete="email"
                  className={`w-full px-4 pt-3 pb-3 border-2 ${emailError ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:outline-none focus:border-blue-600`}
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              {/* Password Input */}
              <div className="relative mb-2 group">
                <label className="absolute -top-3 left-3 px-1 text-sm font-medium text-gray-700 bg-neutral-100 z-10 group-focus-within:text-blue-600" htmlFor="password">Password</label>
                <input 
                  type={showPassword ? "text" : "password"}
                  id="password"
                  className={`w-full pr-10 px-4 pt-3 pb-3 border-2 ${passwordError ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:outline-none focus:border-blue-600`}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                {password && (
                  <div className="absolute inset-y-0 right-3 flex items-center cursor-pointer" onClick={() => setShowPassword(!showPassword)}>
                    {showPassword ? <HiEye className="text-gray-500" size={24} /> : <HiEyeSlash className="text-gray-500" size={24} />}
                  </div>
                )}
              </div>

              {/* Remember Me Checkbox */}
              <div className="flex items-center mb-2">
                <label className="flex items-center">
                  <input 
                    type="checkbox"
                    id="rememberMe"
                    className="mr-2 h-4 w-4 align-middle"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <span className="text-gray-700 align-middle">Remember Me</span>
                </label>
              </div>

              {/* Error Message */}
                {error && (
                    <div className="mb-3 flex items-center text-sm text-red-500">
                        <MdError className="mr-2" />
                        <span>{error}</span>
                    </div>
                )}

              <div className="mb-4">
                <button type="submit" className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">Sign In</button>
              </div>
              <div className="text-center mb-4">
                <div className="text-black mb-2">
                  <p>Don't have an account? <Link to="/register" className='text-blue-500 font-bold'>Create one</Link></p>
                </div>
                <div className="flex items-center">
                  <hr className="flex-grow border-t border-gray-300" />
                  <span className="mx-4 text-gray-600">or</span>
                  <hr className="flex-grow border-t border-gray-300" />
                </div>
              </div>
              <div>
                <button type="button" className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">Continue with Google</button>
              </div>
            </form>
            {/* Back Arrow Icon */}
            <div className="absolute top-4 left-4">
              <Link to="/welcome" className="text-blue-500 text-4xl hover:text-blue-600">
                <BsArrowLeftShort />
              </Link>
            </div>
          </div>
        </div>
      </div>
      {/* Footer */}
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
