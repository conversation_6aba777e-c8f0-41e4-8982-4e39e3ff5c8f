[2025-07-10 01:52:03] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(116): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token co...', NULL, Object(Lcobucci\\JWT\\Validation\\RequiredConstraintsViolated))
#1 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#38 {main}

[previous exception] [object] (Lcobucci\\JWT\\Validation\\RequiredConstraintsViolated(code: 0): The token violates some mandatory constraints, details:
- Token signature mismatch at C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\lcobucci\\jwt\\src\\Validation\\RequiredConstraintsViolated.php:24)
[stacktrace]
#0 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\lcobucci\\jwt\\src\\Validation\\Validator.php(23): Lcobucci\\JWT\\Validation\\RequiredConstraintsViolated::fromViolations(Object(Lcobucci\\JWT\\Validation\\ConstraintViolation))
#1 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(114): Lcobucci\\JWT\\Validation\\Validator->assert(Object(Lcobucci\\JWT\\Token\\Plain), Object(Lcobucci\\JWT\\Validation\\Constraint\\LooseValidAt), Object(Lcobucci\\JWT\\Validation\\Constraint\\SignedWith))
#2 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#4 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#6 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#7 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#8 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#9 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#10 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#39 {main}
"} 
[2025-07-10 01:52:03] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:516)
[stacktrace]
#0 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(862): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(258): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(118): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(105): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(88): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#8 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#37 {main}
"} 
[2025-07-10 01:54:30] local.ERROR: The resource owner or authorization server denied the request. {"exception":"[object] (League\\OAuth2\\Server\\Exception\\OAuthServerException(code: 9): The resource owner or authorization server denied the request. at C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\Exception\\OAuthServerException.php:243)
[stacktrace]
#0 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(116): League\\OAuth2\\Server\\Exception\\OAuthServerException::accessDenied('Access token co...', NULL, Object(Lcobucci\\JWT\\Validation\\RequiredConstraintsViolated))
#1 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#2 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#4 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#6 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#7 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#8 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#9 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#38 {main}

[previous exception] [object] (Lcobucci\\JWT\\Validation\\RequiredConstraintsViolated(code: 0): The token violates some mandatory constraints, details:
- Token signature mismatch at C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\lcobucci\\jwt\\src\\Validation\\RequiredConstraintsViolated.php:24)
[stacktrace]
#0 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\lcobucci\\jwt\\src\\Validation\\Validator.php(23): Lcobucci\\JWT\\Validation\\RequiredConstraintsViolated::fromViolations(Object(Lcobucci\\JWT\\Validation\\ConstraintViolation))
#1 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\AuthorizationValidators\\BearerTokenValidator.php(114): Lcobucci\\JWT\\Validation\\Validator->assert(Object(Lcobucci\\JWT\\Token\\Plain), Object(Lcobucci\\JWT\\Validation\\Constraint\\LooseValidAt), Object(Lcobucci\\JWT\\Validation\\Constraint\\SignedWith))
#2 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\league\\oauth2-server\\src\\ResourceServer.php(84): League\\OAuth2\\Server\\AuthorizationValidators\\BearerTokenValidator->validateAuthorization(Object(Nyholm\\Psr7\\ServerRequest))
#3 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(232): League\\OAuth2\\Server\\ResourceServer->validateAuthenticatedRequest(Object(Nyholm\\Psr7\\ServerRequest))
#4 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(178): Laravel\\Passport\\Guards\\TokenGuard->getPsrRequestViaBearerToken(Object(Illuminate\\Http\\Request))
#5 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\passport\\src\\Guards\\TokenGuard.php(120): Laravel\\Passport\\Guards\\TokenGuard->authenticateViaBearerToken(Object(Illuminate\\Http\\Request))
#6 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(56): Laravel\\Passport\\Guards\\TokenGuard->user()
#7 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(83): Laravel\\Passport\\Guards\\TokenGuard->check()
#8 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(62): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#9 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#10 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\laragon\\\\www\\\\...')
#39 {main}
"} 
