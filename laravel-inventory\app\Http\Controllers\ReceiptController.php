<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Receipt;
use App\Models\Inventory;
use Illuminate\Support\Facades\Log;

class ReceiptController extends Controller
{
    public function createReceipt(Request $request)
    {
        try {
            // Validate input data
            $validated = $request->validate([
                'date' => 'required|date',
                'items' => 'required|array',
                'items.*.category_name' => 'required|string|max:255',
                'items.*.item_name' => 'required|string|max:255',
                'items.*.qty_of_item' => 'required|integer',
                'items.*.qty_of_pack' => 'required|integer',
                'items.*.price' => 'required|numeric|min:0',
                'items.*.totalAmount' => 'required|numeric|min:0',
                'items.*.store' => 'required|string|max:255',
                'items.*.receipt_no' => 'required|string|max:255',
            ]);

            // Initialize totals for items, packs, and grand total
            $totalItems = 0;
            $totalPacks = 0;
            $grandTotal = 0;
            $itemCount = count($validated['items']);

            // Calculate total items, packs, and grand total
            foreach ($validated['items'] as $item) {
                $totalItems += $item['qty_of_item'];
                $totalPacks += $item['qty_of_pack'];
                $grandTotal += $item['totalAmount'];
            }

            // Create receipt with total_item, total_pack, grand_total, and item count
            $receipt = Receipt::create([
                'date' => $validated['date'],
                'item' => $itemCount,        // Number of rows/items added
                'total_item' => $totalItems,   // Total quantity of items
                'total_pack' => $totalPacks,   // Total quantity of packs
                'grand_total' => $grandTotal,  // Grand total for the receipt
            ]);

            // Store items in the inventory table
            foreach ($validated['items'] as $item) {
                $inventoryItem = Inventory::create([
                    'receipt_id' => $receipt->id, 
                    'date' => $validated['date'],
                    'category_name' => $item['category_name'],
                    'item_name' => $item['item_name'],
                    'qty_of_item' => $item['qty_of_item'],
                    'qty_of_pack' => $item['qty_of_pack'],
                    'price' => $item['price'],
                    'totalAmount' => $item['totalAmount'],
                    'store' => $item['store'],
                    'receipt_no' => $item['receipt_no'],
                ]);
            
                // Update the stock status after the item is created
                $inventoryItem->stock_status = (new InventoryController)->checkStockStatus($inventoryItem);
                $inventoryItem->save(); // Save the updated status
            }

            return response()->json(['message' => 'Receipt created successfully', 'receipt' => $receipt], 200);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['message' => 'Error creating receipt'], 500);
        }
    }

    public function showReceipts()
    {
        $receipts = Receipt::get();
        return response()->json($receipts, 200);
    }

    public function showTrashedReceipts()
    {
        $receipts = Receipt::withTrashed()->get();
        return response()->json($receipts, 200);
    }

    public function softDeleteReceipt($id)
    {
        $receipt = Receipt::find($id);
        if ($receipt) {
            if ($receipt->inventories()->exists()) {
                foreach ($receipt->inventories as $inventory) {
                    $inventory->delete();
                }
            }
            $receipt->delete();
            return response()->json(['message' => 'Receipt and its inventories soft deleted successfully'], 200);
        } else {
            return response()->json(['message' => 'Receipt not found'], 404);
        }
    }
}
