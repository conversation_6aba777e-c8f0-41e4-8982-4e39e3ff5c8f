import React, { useState, useEffect } from 'react'
import DashboardStatsGrid from '../pages/DashboardStatsGrid'
import { api } from '../../api/apiService'

export const Dashboard = () => {
  const [inventory, setInventory] = useState([])
  const [loading, setLoading] = useState(true)
  const [stockLevels, setStockLevels] = useState([])
  const [totalProducts, setTotalProducts] = useState(0)
  const [lowStockCount, setLowStockCount] = useState(0)
  const [totalCategories, setTotalCategories] = useState(0)

  useEffect(() => {
    const fetchInventory = async () => {
      try {
        const response = await api.get('/inventory')
        setInventory(response.data)
        setLoading(false)
        calculateStockLevels(response.data)
        calculateTotalProducts(response.data)
        calculateLowStockCount(response.data)
        calculateTotalCategories(response.data)
      } catch (error) {
        console.error('Error fetching inventory:', error)
        setLoading(false)
      }
    }

    fetchInventory()
  }, [])

  const calculateStockLevels = (data) => {
    const inStock = data.filter(item => item.qty_of_item > 10).length
    const lowStock = data.filter(item => item.qty_of_item > 0 && item.qty_of_item <= 10).length
    const outOfStock = data.filter(item => item.qty_of_item === 0).length

    setStockLevels([
      { name: 'In Stock', value: inStock },
      { name: 'Low Stock', value: lowStock },
      { name: 'Out of Stock', value: outOfStock },
    ])
  }

  const calculateTotalProducts = (data) => {
    const uniqueItems = new Set(data.map(item => item.item_name))
    setTotalProducts(uniqueItems.size)
  }

  const calculateLowStockCount = (data) => {
    const lowStockCount = data.filter(item => item.qty_of_item > 0 && item.qty_of_item <= 10).length
    setLowStockCount(lowStockCount)
  }

  const calculateTotalCategories = (data) => {
    const uniqueCategories = new Set(data.map(item => item.category_name))
    setTotalCategories(uniqueCategories.size)
  }

  return (
    <>
      {loading ? (
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
          <div className="flex justify-center">
            <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="none" stroke="currentColor" strokeWidth="4" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading inventory...</span>
          </div>
        </div>
      ) : (
        <div>
          <DashboardStatsGrid
            inventory={inventory}
            stockLevels={stockLevels}
            totalProducts={totalProducts}
            lowStockCount={lowStockCount}
            totalCategories={totalCategories}
          />
        </div>
      )}
    </>
  )
}
