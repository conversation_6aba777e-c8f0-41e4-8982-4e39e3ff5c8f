import React from 'react';
import { Link } from 'react-router-dom';

export const PrivacyPolicy = () => {
  return (
    <div className="relative min-h-screen bg-gray-100">
      <header className="p-2 text-gray-800 shadow-md bg-blue-950">
        <div className="max-w-screen-xl mx-auto flex items-center justify-between">
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s"
            alt="Company Logo"
            className="w-16 h-16 object-contain"
          />
          <nav className="flex space-x-4 mr-5">
            <Link to="/login" className="text-white hover:text-gray-300 transition duration-300">Login</Link>
            <Link to="/register" className="text-white hover:text-gray-300 transition duration-300">Register</Link>
          </nav>
        </div>
      </header>

      <div className="flex items-center justify-center min-h-[calc(100vh-136px)] py-16 bg-sky-700">
        <div className="text-left p-12 bg-white rounded-lg shadow-md max-w-2xl mx-auto">
          <h2 className="text-3xl font-bold mb-4">Privacy Policy</h2>
          <p className="text-gray-800 mb-4">
            At Fligno Inventory Management, we are committed to safeguarding the privacy of our company members. This Privacy Policy explains how we handle your data.
          </p>

          <h3 className="text-xl font-bold mb-2">1. Data Collection</h3>
          <p className="text-gray-800 mb-4">
            We collect personal and usage data to ensure a secure and efficient experience. This data is used to manage internal operations and improve inventory processes.
          </p>

          <h3 className="text-xl font-bold mb-2">2. Use of Information</h3>
          <p className="text-gray-800 mb-4">
            Collected data is used for maintaining services, system security, and enhancing the user experience. We do not use member data for external purposes.
          </p>

          <h3 className="text-xl font-bold mb-2">3. Data Sharing</h3>
          <p className="text-gray-800 mb-4">
            Information is only shared with authorized personnel within the company. We may disclose data to law enforcement if required by law or to protect company interests.
          </p>

          <h3 className="text-xl font-bold mb-2">4. Data Security</h3>
          <p className="text-gray-800 mb-4">
            We implement industry-standard security measures to protect data against unauthorized access, ensuring that only authorized members have access to the system.
          </p>

          <h3 className="text-xl font-bold mb-2">5. User Rights</h3>
          <p className="text-gray-800 mb-4">
            Members have the right to access, correct, or request the deletion of their personal data. For any requests, contact our support team.
          </p>

          <h3 className="text-xl font-bold mb-2">6. Changes to Privacy Policy</h3>
          <p className="text-gray-800 mb-4">
            We reserve the right to update this Privacy Policy at any time. Changes will be communicated through the platform or via email.
          </p>

          <h3 className="text-xl font-bold mb-2">7. Contact Us</h3>
          <p className="text-gray-800">
            For any questions regarding this policy, please contact us at <a href="mailto:<EMAIL>" className="text-blue-500 hover:text-blue-700"><EMAIL></a>.
          </p>
        </div>
      </div>

      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
