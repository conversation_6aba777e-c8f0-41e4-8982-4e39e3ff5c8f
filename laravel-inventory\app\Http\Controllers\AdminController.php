<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use App\Models\User;

class AdminController extends Controller
{
    public function approveUser($id)
    {
        $user = User::find($id);
        if (!$user) {
            return response()->json(['message' => 'User not found.'], 404);
        }

        // Check if the user has completed registration
        if (is_null($user->email_verified_at)) {
            return response()->json(['message' => 'User has not completed registration. Cannot activate until the user sets up a password.'], 400);
        }

        // Toggle the user's status between active and inactive
        $user->status = $user->status === 'active' ? 'pending' : 'active';
        $user->save();

        return response()->json(['message' => 'User status updated successfully.', 'status' => $user->status]);
    }

    public function getNonAdminUsers()
    {
        $users = User::where('is_admin', false)->get(['id', 'name', 'email', 'status']);
        return response()->json($users);
    }

    public function getPendingUsers()
    {
        $pendingUsers = User::where('status', 'pending')->get();
        return response()->json($pendingUsers);
    }

    public function addAuthorizedPersonnel(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'email' => 'required|email|unique:authorized_personnel,email'
        ]);
        
        // Insert authorized personnel into the database using DB::table()
        DB::table('authorized_personnel')->insert([
            'email' => $request->email,
            'created_at' => now(),  // Automatically set the current timestamp
            'updated_at' => now()
        ]);

        // Return the inserted personnel data
        return response()->json([
            'email' => $request->email,
            'message' => 'Authorized personnel added successfully.'
        ]);
    }

    public function removeAuthorizedPersonnel($id)
    {
        // Find the authorized personnel by ID
        $person = DB::table('authorized_personnel')->where('id', $id)->first();
        if (!$person) {
            return response()->json(['message' => 'Personnel not found.'], 404);
        }
    
        // Get the email of the authorized personnel to find the associated user
        $email = $person->email;
    
        // Delete the user from the users table based on the email
        $user = DB::table('users')->where('email', $email)->first();
        if ($user) {
            DB::table('users')->where('email', $email)->delete();
        }
    
        // Delete the authorized personnel entry
        DB::table('authorized_personnel')->where('id', $id)->delete();
    
        return response()->json(['message' => 'Authorized personnel and associated user removed successfully.']);
    }    

    public function getAuthorizedPersonnel()
    {
        // Fetch all authorized personnel
        $personnel = DB::table('authorized_personnel')->get();
        return response()->json($personnel);
    }
}
