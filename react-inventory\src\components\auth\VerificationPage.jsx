import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { MdError } from 'react-icons/md';
import { api } from '../../api/apiService';

export const VerificationPage = () => {
  const { token } = useParams();
  const [loadingEmail, setLoadingEmail] = useState(true);
  const [countdown, setCountdown] = useState(59);
  const [canResend, setCanResend] = useState(false);
  const [verificationCode, setVerificationCode] = useState(Array(6).fill(''));
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [email, setEmail] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const inputRefs = useRef([]);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchVerificationData = async () => {
      if (!token) {
        navigate('/register');
        return;
      }

      setLoadingEmail(true);
      try {
        // Fetch email and other data
        const emailResponse = await api.get('/get-email');
        const email = emailResponse.data.email;
        setEmail(email);
        localStorage.setItem('email', email);

        // Check password set status
        const passwordResponse = await api.get('/auth/password-set', {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (!passwordResponse.data.passwordSet) {
          navigate('/register/verification-page/set-password');
          return;
        }

        // Set email verified status
        const verificationResponse = await api.post('/verify', { email, token });

        // const verificationResponse = await api.get('/verify', {
        //   params: { email, token }
        // });

        // Optionally handle response and set verification status
        if (verificationResponse.data.verification_code) {
          setVerificationCode(verificationResponse.data.verification_code.split(''));
          localStorage.setItem('emailVerified', 'true');
          setIsVerified(true);
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // Token is invalid or expired
          localStorage.removeItem('authToken');
          navigate('/register');
        }
      } finally {
        setLoadingEmail(false);
      }
    };

    fetchVerificationData();
  }, [navigate, token]);

  useEffect(() => {
    if (isVerified) {
      navigate('/login');
    }
  }, [isVerified, navigate]);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown((prev) => prev - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);

  if (loadingEmail) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  const handleChange = (value, index) => {
    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);

    if (value !== '' && index < 5) {
      inputRefs.current[index + 1].focus();
    }

    if (value !== '' && isError) {
      setIsError(false);
      setErrorMessage('');
    }

    if (newCode.join('').length === 6) {
      validateCode(newCode.join(''));
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace') {
      const newCode = [...verificationCode];
      newCode[index] = '';
      setVerificationCode(newCode);
      if (index > 0) {
        inputRefs.current[index - 1].focus();
      }
      if (isError) {
        setIsError(false);
        setErrorMessage('');
      }
    }
  };

  const validateCode = async (code) => {
    try {
      const response = await api.post('/verify', { email, code, token });
      if (response.status === 200) {
        setIsVerified(true); // Set verification status to true
        navigate(`/register/verification-page/${token}/${response.data.verification_code}`);
      } else {
        setIsError(true);
        setErrorMessage(response.data.message || 'The verification code is incorrect. Please try again.');
      }
    } catch (error) {
      setIsError(true);
      const message = error.response?.data?.message || 'An unexpected error occurred. Please try again.';
      setErrorMessage(message);
      resetVerificationCode();
    }
  };

  const resetVerificationCode = () => {
    setVerificationCode(Array(6).fill(''));
    inputRefs.current[0].focus();
  };

  const handleResend = async () => {
    if (!canResend) return;

    setCountdown(59);
    setCanResend(false);

    try {
      await api.post('/resend-code', { email, token });
      setIsError(false);
      setErrorMessage('A new verification code has been sent to your email.');
    } catch (error) {
      setIsError(true);
      setErrorMessage('Failed to resend the verification code. Please try again.');
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-blue-950">
      <div className="flex-grow flex items-center justify-center">
        <div className="relative w-[540px] h-[540px] bg-white rounded-lg shadow-md border">
          <div className="absolute inset-0 px-[70px] py-[48px] flex flex-col justify-start space-y-6">
            <Link to="/register" className="text-black font-medium hover:underline">
              &lt; Back
            </Link>
            <h2 className="text-2xl font-bold mb-1">Enter verification code</h2>
            <p className="text-gray-600">
              The verification code has been sent to your email <strong>{email}</strong>
            </p>

            <div className="flex justify-between items-center mb-6 space-x-2">
              {verificationCode.map((value, index) => (
                <input
                  key={index}
                  type="text"
                  maxLength="1"
                  value={value}
                  onChange={(e) => handleChange(e.target.value.replace(/\D/g, ''), index)}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  ref={(el) => (inputRefs.current[index] = el)}
                  className={`w-12 h-12 text-center text-xl border-2 rounded-md focus:outline-none focus:ring-2 ${
                    isError && value
                      ? 'border-red-500 focus:ring-red-300'
                      : isError
                      ? 'border-red-500 focus:ring-red-300'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-300'
                  }`}
                />
              ))}
            </div>

            {/* Error Message */}
            {isError && (
              <div className="flex items-center text-red-500 mb-4">
                <MdError className="mr-2" />
                <span>{errorMessage}</span>
              </div>
            )}

            <div className="text-left text-gray-500">
              <p>
                Not received yet?{' '}
                {canResend ? (
                  <button
                    onClick={handleResend}
                    className="text-blue-500 underline hover:text-blue-700"
                  >
                    Resend verification code
                  </button>
                ) : (
                  `Resend after ${countdown} seconds`
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
