# Inventory System - A Learn@Fligno Project

## Overview
The Inventory System is a centralized application built with a **Laravel** backend for managing inventory items, user authorization, and administrative functions, and a **React** frontend for user interaction. This README provides guidance on setting up the project and using its features.

## Prerequisites
- **Local Development Environment**: You can use software like [Laragon](https://laragon.org/) or [XAMPP](https://www.apachefriends.org/index.html).
- **PHP**: Ensure PHP is installed on your system for the Laravel framework.
- **Composer**: Required for managing PHP dependencies.
- **Node.js and npm**: Required for the React frontend, which uses the Vite framework, and npm is needed for package installation.

## Setup Instructions

### 1. Environment Configuration
To set up the application, you need to configure your environment variables in the `.env` file. This file is crucial for accessing your local PHPMyAdmin and configuring mail settings.

#### Database Configuration
- Make sure you have a database created in PHPMyAdmin named `laravel-inventory`.
- Update your `.env` file with your database credentials.

#### Mail Configuration
For email notifications (e.g., sending verification codes), configure the mail settings in your `.env` file as follows:
```plaintext
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>      # Replace with your email
MAIL_PASSWORD=your-app-password         # Use an app password generated for your Gmail
MAIL_ENCRYPTION=tls
```

### 2. Database Migration
After setting up your database and environment variables, run the following command to create and store the collections in your database:
```bash
php artisan migrate:fresh
```

### 3. API Authentication
To set up API access using Laravel Passport, run the following command:
```bash/cmd
php artisan passport:client --personal
```

### 4. Seeding the Admin User
To create an admin user, use the following command:
```bash/cmd
php artisan db:seed --class=AdminUserSeeder
```
**Note**: Take note of the email and password used for the admin user, as you will need this to log in.

### 5. Start the Application
In the terminal, navigate to the Laravel project directory and run:
```bash/cmd
php artisan serve
```

Then, in the React project directory, run:
```bash/cmd
npm run dev
```

### 6. Local Development Environment
Ensure your local server (e.g., Laragon, XAMPP) is running to access the application.

## User Management

### Admin User Features
- After logging in, the admin can navigate to the settings page to manage authorized users.
- Authorized users can register with their email, receive a verification code, and set their passwords.

### User Registration
1. Admin authorizes users.
2. Users receive a verification code via email to complete their registration.
3. On first login, users are directed to an approval page while waiting for admin activation.

### Account Activation
The admin can toggle user statuses for accessing the system. Once activated, users can access the dashboard and other features.

## Features of the Inventory System
- **Centralized Data Management**: All users can perform CRUD operations on the inventory.
- **Dynamic Data Visualization**: The dashboard provides charts and stats to monitor inventory status.
- **Inventory Management**: Users can update, sort, monitor, print data, among other features.
- **Receipt Management**: Users can add items through a structured process.

### Navigating the System
- Users can manage receipts to add items to the inventory.
- A table displays recently added data with active tabs for further details.
- The dashboard allows for monitoring and tracking inventory status.

## Future Improvements
- Implement a monthly tracking feature to avoid data overload.
- Create a purchasing page for pre-receipt planning.
- Additional enhancements to improve user experience and functionality.

## Conclusion
This Inventory System has room for improvements and is designed to streamline inventory management processes. Contributions and feedback are welcome!
