<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Inventory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'receipt_id', 'category_name', 'date', 'item_name', 'qty_of_item',
        'qty_of_pack', 'price', 'totalAmount', 'store',
        'receipt_no', 'grand_total', 'stock_status'
    ];

    public function receipt()
    {
        return $this->belongsTo(Receipt::class);
    }
    
    protected static function booted()
    {
        static::updated(function ($inventory) {
            $inventory->receipt->updateTotals();
        });
    
        static::deleted(function ($inventory) {
            $receipt = $inventory->receipt;
            $receipt->updateTotals();
        });
    }
}
