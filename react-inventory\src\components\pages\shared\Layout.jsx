import React, { useState, useEffect } from 'react';
import { Outlet, Navigate, useNavigate } from 'react-router-dom';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { api } from '../../../api';

export const Layout = () => {
  const [open, setOpen] = useState(true);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null); // Track error state
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUserData = async () => {
        const authToken = localStorage.getItem('authToken');

        if (!authToken) {
            navigate('/login');
            return;
        }

        try {
            const response = await api.get('/user-data', {
                headers: { Authorization: `Bearer ${authToken}` },
            });
            setUser(response.data);
        } catch (error) {
            console.error('Error fetching user data:', error.message);
            setError('Failed to load user data. Please try again later.');

            // Check if the error is due to an invalid token
            if (error.response.status === 401) {
                // Remove the invalid token from local storage
                localStorage.removeItem('authToken');

                // Redirect the user to the login page
                navigate('/login');
            }
        }
    };

    fetchUserData();
}, [navigate]);
  
  if (user === null) {
    return <div>Loading...</div>;
  }

  if (error) {
    return (
      <div className="error-message">
        <h2>Error</h2>
        <p>{error}</p>
      </div>
    );
  }

  const { is_admin, status } = user;
  
  if (status === 'pending') {
    return <Navigate to="/approval" />;
  }

  return (
    <div className="flex flex-col h-screen w-screen">
      <div className="flex-1 flex flex-row bg-gray-500">
        <Sidebar open={open} setOpen={setOpen} isAdmin={is_admin} />
        <div className='flex-1'>
          <Header isAdmin={is_admin} />
          <div className='pt-2'>{<Outlet />}</div>
        </div>
      </div>
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};