<?php

use App\Http\Controllers\{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Auth<PERSON><PERSON>roller, LoginController, VerificationController, 
    PasswordController, DashboardController, LogoutController, 
    InventoryController, CategoryController, ReceiptController
};
use App\Http\Middleware\RedirectIfPasswordIsSet;
use Illuminate\Support\Facades\Route;

// Registration and Authentication Routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [LoginController::class, 'login']);

// Email and Password Related Routes
Route::middleware('auth:api')->group(function () {
    Route::get('/auth/check', [AuthController::class, 'check']);
    Route::get('/get-email', [AuthController::class, 'getEmail']);
    Route::get('/auth/password-set', [AuthController::class, 'isPasswordSet']);
    Route::post('/resend-code', [AuthController::class, 'resendVerificationCode']);
    
    // Protect Verification and Password Routes for Users Who Haven't Set Their Password
    Route::middleware(RedirectIfPasswordIsSet::class)->group(function () {
        Route::get('/register/verification-page', [AuthController::class, 'getVerificationPage']);
        Route::post('/verify', [VerificationController::class, 'verify']);
        Route::post('/set-password', [PasswordController::class, 'setPassword']);
    });
});

// User Data and Role
Route::middleware('auth:api')->group(function () {
    Route::get('/user-data', [LoginController::class, 'getUserData']);
    Route::get('/user-role', [LoginController::class, 'getUserRole']);
    Route::get('/user-status', [LoginController::class, 'userStatus']);
    Route::post('/logout', [LogoutController::class, 'logout']);
});

// Authenticated and Verified Routes
Route::middleware(['auth:api', 'verified'])->group(function () {
    Route::get('/dashboard/{id}', [DashboardController::class, 'index']);

    // Notification Routes
    Route::get('/notifications', [InventoryController::class, 'getNotifications']);
    Route::get('/notification-count', [InventoryController::class, 'getNotificationCount']);
    Route::post('/create-notification', [InventoryController::class, 'createNotification']);
    Route::post('/notifications/mark-as-read', [InventoryController::class, 'markNotificationAsRead']);
    Route::post('/notifications/remove-all', [InventoryController::class, 'removeAllNotifications']);

    // Inventory Routes
    Route::prefix('inventory')->group(function () {
        Route::get('/', [InventoryController::class, 'showInventory']);
        Route::put('/edit/{id}', [InventoryController::class, 'updateInventory']);
        Route::delete('/delete/{id}', [InventoryController::class, 'destroy']);
    });

    // Category Routes
    Route::prefix('category')->group(function () {
        Route::get('/', [CategoryController::class, 'showCategories']);
        Route::post('/create', [CategoryController::class, 'createCategory']);
        Route::delete('/delete/{id}', [CategoryController::class, 'destroy']);
    });

    // Receipt Routes
    Route::post('/receipts', [ReceiptController::class, 'createReceipt']);
    Route::get('/receipts', [ReceiptController::class, 'showReceipts']);
    Route::get('/receipts/trashed', [ReceiptController::class, 'showTrashedReceipts']);
    Route::delete('/receipts/soft-delete/{id}', [ReceiptController::class, 'softDeleteReceipt']);
});

// Admin Routes
Route::middleware(['auth:api', 'admin'])->prefix('settings')->group(function () {
    Route::get('/users', [AdminController::class, 'getNonAdminUsers']);
    Route::get('/authorized-personnel', [AdminController::class, 'getAuthorizedPersonnel']);
    Route::post('/add-authorized-personnel', [AdminController::class, 'addAuthorizedPersonnel']);
    Route::post('/remove-authorized-personnel/{id}', [AdminController::class, 'removeAuthorizedPersonnel']);
    Route::post('/toggle-status/{id}', [AdminController::class, 'approveUser']);
});
