import React from 'react';
import { FaBox, FaTag, FaShippingFast, FaExclamationTriangle } from 'react-icons/fa';
import { ResponsiveContainer, Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line } from 'recharts';

const DashboardStatsGrid = ({ stockLevels, totalProducts, lowStockCount, totalCategories }) => {
  // Hardcoded restocks level
  const dataRestocksAlerts = [
    { name: 'Restocks', value: 0 },
    { name: 'Low Stock Alerts', value: lowStockCount },
  ];

  return (
    <div className="p-4 w-full">
      {/* Dashboard Header and Stats */}
      <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-6">
        <h1 className="text-xl font-semibold mb-4">Inventory Dashboard</h1>
        <hr className="my-4 border-gray-300" />

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
          <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 flex items-center">
            <FaBox className="text-blue-500 text-3xl mr-4" />
            <div>
              <h3 className="text-blue-500 text-medium font-bold flex items-center">
                Categories
              </h3>
              <p className="text-2xl font-bold">{totalCategories}</p>
              <span className="text-xs text-gray-400">Total Categories</span>
            </div>
          </div>
          <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 flex items-center">
            <FaTag className="text-amber-500 text-3xl mr-4" />
            <div>
              <h3 className="text-amber-500 text-medium font-bold flex items-center">
                Total Products
              </h3>
              <p className="text-2xl font-bold">{totalProducts}</p>
              <span className="text-xs text-gray-400">Products in Inventory</span>
            </div>
          </div>
          <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 flex items-center">
            <FaShippingFast className="text-violet-500 text-3xl mr-4" />
            <div>
              <h3 className="text-violet-500 text-medium font-bold flex items-center">
                Total Restocks
              </h3>
              <p className="text-2xl font-bold">0</p>
              <span className="text-xs text-gray-400">Restocks This Month</span>
            </div>
          </div>
          <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 flex items-center">
            <FaExclamationTriangle className="text-red-500 text-3xl mr-4" />
            <div>
              <h3 className="text-red-500 text-medium font-bold flex items-center">
                Low Stock Alerts
              </h3>
              <p className="text-2xl font-bold">{lowStockCount}</p>
              <span className="text-xs text-gray-400">Items Low in Stock</span>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory Status Graphs */}
      <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
        <h2 className="text-xl font-semibold mb-4">Inventory Status Overview</h2>
        <hr className="my-4 border-gray-300" />
      
        {/* Graphs Section */}
        <div className="flex flex-wrap justify-center">
          <div className="w-full md:w-1/2 xl:w-1/2 p-4">
            {/* Stock Levels Graph */}
            <div className="mb-2 text-center">
              <h3 className="text-gray-700 font-semibold">Stock Levels</h3>
            </div>
            <ResponsiveContainer height={300}>
              <BarChart data={stockLevels}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={{ textAnchor: 'middle', dy: 5 }} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="w-full md:w-1/2 xl:w-1/2 p-5">
            {/* Restocks & Alerts Graph */}
            <div className="mb-4 text-center">
              <h3 className="text-gray-700 font-semibold">Restocks & Alerts</h3>
            </div>
            <ResponsiveContainer height={300}>
              <LineChart data={dataRestocksAlerts}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={{ textAnchor: 'middle', dy: 5 }} />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="value" stroke="#82ca9d" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardStatsGrid;
