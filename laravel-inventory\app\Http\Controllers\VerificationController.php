<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class VerificationController extends Controller
{
    public function verify(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required|numeric|digits:6'
        ]);

        $user = User::where('email', $request->input('email'))->first();

        if (!$user) {
            return response()->json(['message' => 'User not found.'], 404);
        }

        if ($user->verification_code == $request->input('code')) {
            // Check if password is already set
            if ($user->password) {
                return response()->json(['message' => 'Email already verified.'], 200);
            }

            // Generate a token
            $token = $user->createToken('Verifying-Email')->accessToken;

            return response()->json([
                'message' => 'Email verified.',
                'email' => $user->email,
                'verification_code' => $user->verification_code,
                'token' => $token
            ], 200);
        }

        return response()->json(['message' => 'Invalid verification code.'], 403);
    }
}
