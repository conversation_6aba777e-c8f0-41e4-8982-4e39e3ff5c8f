<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class EnsurePasswordIsSet
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Check if the authenticated user has set a password
        $user = Auth::user();
        if (!$user || !$user->password) {
            return response()->json([
                'message' => 'You must set your password before accessing this route.'
            ], 403);  // Return a 403 Forbidden if password is not set
        }

        return $next($request);
    }
}
