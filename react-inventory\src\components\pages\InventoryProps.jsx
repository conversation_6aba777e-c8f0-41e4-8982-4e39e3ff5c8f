import React, { useState, useEffect } from 'react'
import { format, parse } from 'date-fns'
import { FaBox, FaTag, FaShippingFast, FaExclamationTriangle, FaInfoCircle, FaEdit, FaSave, FaTrashAlt } from 'react-icons/fa'
import InventoryFilters from './InventoryFilters'
import { api } from '../../api/apiService'
import Modal from 'react-modal'
import jsPDF from 'jspdf'
import 'jspdf-autotable'

const InventoryProps = ({
  STOCK_THRESHOLD,
  activeTab,
  inventory,
  receipts,
  setReceipts,
  categories,
  totalCategories,
  restocksCount,
  editingIndex,
  newProduct,
  setNewProduct,
  handleEditProduct,
  handleSaveProduct,
  loading,
  errors,
  inventoryCurrentPage,
  setInventoryCurrentPage,
  handleFilterChange,
  filteredInventory,
  setFilteredInventory,
}) => {
  // Constants for Inventory Overview
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [idToDelete, setIdToDelete] = useState(null)

  const totalProductsCount = inventory.reduce((acc, product) => acc + product.qty_of_item, 0)
  const totalValue = inventory.reduce((acc, product) => acc + parseFloat(product.totalAmount), 0)
  
  const isEditing = editingIndex !== null
  
  const [inventoryTotalPages, setInventoryTotalPages] = useState(0)

  const [overallCurrentPage, setOverallCurrentPage] = useState(1)
  const [overallTotalPages, setOverallTotalPages] = useState(0)
  const [itemsPerPage] = useState(5)

  useEffect(() => {
    if (activeTab === 'overall') {
      setOverallTotalPages(Math.ceil(receipts.length / itemsPerPage))
    } else {
      const inventoryToUse = filteredInventory.length > 0 ? filteredInventory : inventory
      setInventoryTotalPages(Math.ceil(inventoryToUse.length / itemsPerPage))
    }
  }, [activeTab, inventory, receipts, itemsPerPage, filteredInventory])

  const handlePrintData = () => {
    const doc = new jsPDF()
    const data = activeTab === 'overall' ? receipts : inventory
  
    doc.text('Fligno Overall Inventory Report', 10, 10)
  
    const tableData = data.map((receipt) => [
      receipt.id,
      receipt.item,
      receipt.date,
      receipt.total_item,
      receipt.total_pack,
      receipt.grand_total,
    ])
  
    doc.autoTable({
      head: [['Receipt ID', 'Item', 'Receipt Date', 'Total Item (Qty)', 'Total Pack (Qty)', 'Grand Total']],
      body: tableData,
    })

    // Add a footer text at the bottom of the page
    const footerText = 'This serves as the initial overall inventory report.'
    const footerY = 270
    
    doc.text(footerText, 70, footerY, null, null, 'center')
  
    const string = doc.output('bloburl')
    const iframe = document.createElement('iframe')
    iframe.src = string
    iframe.style.position = 'fixed'
    iframe.onload = function() {
      iframe.contentWindow.print()
    }
    document.body.appendChild(iframe)
  }

  const renderEmptyState = () => (
    <tr>
      <td colSpan="12" className="text-center p-6">
        <FaInfoCircle className="text-blue-500 text-5xl mx-auto mb-4" />
        <p className="text-lg font-semibold text-gray-600">You don't have any products in the inventory.</p>
      </td>
    </tr>
  )

  const handleDeleteProduct = async (id) => {
    setIdToDelete(id)
    setIsModalOpen(true)
  }
  
  const handleConfirmDelete = async () => {
    try {
      const response = await api.delete(`/receipts/soft-delete/${idToDelete}`)
      if (response.status === 200) {
        // Update the UI to reflect the deletion
        setReceipts((prevReceipts) => prevReceipts.filter((receipt) => receipt.id !== idToDelete))
      } else {
        console.error('Error deleting receipt:', response.error)
      }
    } catch (error) {
      console.error('Error deleting receipt:', error)
    }
    setIsModalOpen(false)
  }
  
  const handleCancelDelete = () => {
    setIsModalOpen(false)
  }

 
  // Render table rows for Inventory
  const renderInventoryTableRows = () => {
    const dataToRender = filteredInventory.length > 0 ? filteredInventory : inventory
    const startIndex = (inventoryCurrentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return dataToRender.slice(startIndex, endIndex).map((product) => (
      <tr key={product.id}>
        <td className="px-4 py-2 border border-gray-300 text-center">{product.receipt_id}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">
          {parse(product.date, 'yyyy-MM-dd', new Date()).toLocaleDateString()}
        </td>
        <td className="px-4 py-2 border border-gray-300 text-center">{product.category_name}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{product.item_name}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">
          {product.qty_of_item} {product.qty_of_item === 1 ? 'item' : product.qty_of_item === 0 ? 'item' : 'items'}
        </td>
        <td className="px-4 py-2 border border-gray-300 text-center">
          {product.qty_of_pack} {product.qty_of_pack === 1 ? 'pack' : product.qty_of_pack === 0 ? 'pack' : 'packs'}
        </td>
        <td className="px-4 py-2 border border-gray-300 text-center">₱{product.price}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">₱{product.totalAmount}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{product.store}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{product.receipt_no}</td>
        <td className={`px-4 py-2 border border-gray-300 text-center ${product.qty_of_item === 0 ? 'text-red-500' : product.qty_of_item <= 10 ? 'text-amber-500' : 'text-green-600'}`}>
          {product.stock_status}
        </td>
        <td className="px-4 py-2 border border-gray-300 text-center">
          <button className="text-amber-500 hover:text-amber-700" onClick={() => handleEditProduct(product.id)}>
            <FaEdit />
          </button>
        </td>
      </tr>
    ))
  }
  
    
    /// Render Inventory
    const renderInventory = () => (
      <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-6">
        <h2 className="text-xl font-semibold">Inventory Details</h2>
        <hr className="my-4 border-gray-300" />
        <div className="flex justify-between items-center mb-4">
          <div className="space-x-4 ml-auto">
              <InventoryFilters categories={categories} inventory={inventory} onFilterChange={handleFilterChange} filteredInventory={filteredInventory} setFilteredInventory={setFilteredInventory} />
          </div>
        </div>
        <table className="min-w-full border-collapse">
          <thead>
            <tr className="bg-gray-300">
              <th className="px-4 py-2 border border-gray-400">Receipt ID</th>
              <th className="px-4 py-2 border border-gray-400">Date</th>
              <th className="px-4 py-2 border border-gray-400">Category</th>
              <th className="px-4 py-2 border border-gray-400">Item Name</th>
              <th className="px-4 py-2 border border-gray-400">Quantity of Item</th>
              <th className="px-4 py-2 border border-gray-400">Quantity of Pack</th>
              <th className="px-4 py-2 border border-gray-400">Price</th>
              <th className="px-4 py-2 border border-gray-400">Total Amount</th>
              <th className="px-4 py-2 border border-gray-400">Store</th>
              <th className="px-4 py-2 border border-gray-400">Receipt Number</th>
              <th className="px-4 py-2 border border-gray-400">Availability</th>
              <th className="px-4 py-2 border border-gray-400">Edit</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td className="text-center p-4" colSpan="12">
                  <div className="flex justify-center mt-4">
                    <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="none" stroke="currentColor" strokeWidth="4" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Loading inventory...</span>
                  </div>
                </td>
              </tr>
            ) : inventory.length === 0 ? (
              renderEmptyState()
            ) : (
              renderInventoryTableRows()
            )}
            {isEditing && renderUpdateItemRow()}
          </tbody>
        </table>
        {/* Pagination Controls for Inventory */}
        <div className="flex justify-between items-center mt-4">
          <button
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100"
            onClick={() => setInventoryCurrentPage(inventoryCurrentPage - 1)}
            disabled={inventoryCurrentPage === 1 || isEditing}
          >
            Previous
          </button>
          <span>
            Page {inventoryCurrentPage} of {inventoryTotalPages}
          </span>
          <button
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100"
            onClick={() => setInventoryCurrentPage(inventoryCurrentPage + 1)}
            disabled={inventoryCurrentPage === inventoryTotalPages || inventory.length === 0 || isEditing}
          >
            Next
          </button>
        </div>
      </div>
    )
    
  // Render table rows for Overall Inventory
  const renderOverallInventoryTableRows = () => {
    const startIndex = (overallCurrentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return receipts.slice(startIndex, endIndex).map((receipt, idx) => (
      <tr key={idx}>
        <td className="px-4 py-2 border border-gray-300 text-center">{receipt.id}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{receipt.item}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{parse(receipt.date, 'yyyy-MM-dd', new Date()).toLocaleDateString()}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{receipt.total_item}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">{receipt.total_pack}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">₱{receipt.grand_total}</td>
        <td className="px-4 py-2 border border-gray-300 text-center">
          <button className="text-red-500 hover:text-red-700" onClick={() => handleDeleteProduct(receipt.id)}>
            <FaTrashAlt />
          </button>
          <Modal
            appElement={document.getElementById('root')}
            isOpen={isModalOpen}
            onRequestClose={() => setIsModalOpen(false)}
            className="max-w-md p-6 bg-gray-300 rounded shadow-md fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            >
            <h2 className='text-xl font-semibold mb-2'>Confirm Deletion</h2>
            <p className='text-lg mb-4'>Are you sure you want to delete this receipt?</p>
            <div className="flex justify-center p-3">
              <div className="flex justify-around w-2/3">
                <button className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" onClick={handleConfirmDelete}>Yes</button>
                <button className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onClick={handleCancelDelete}>No</button>
              </div>
            </div>
          </Modal>
        </td>
      </tr>
    ))
  }

  // Render Overall Inventory
  const renderOverallInventory = () => (
    <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-6">
      <h2 className="text-xl font-semibold">Overall Inventory</h2>
      <hr className="my-4 border-gray-300" />
      <div className="flex justify-between items-center mb-4">
        <div className="space-x-4 ml-auto">
          <button
            onClick={handlePrintData}
            className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100"
          >
            Print Data
          </button>
        </div>
      </div>
      <table className="min-w-full border-collapse">
        <thead>
          <tr className="bg-gray-300">
            <th className="px-4 py-2 border border-gray-400">Receipt ID</th>
            <th className="px-4 py-2 border border-gray-400">Item/s</th>
            <th className="px-4 py-2 border border-gray-400">Receipt Date</th>
            <th className="px-4 py-2 border border-gray-400">Total Item (Qty)</th>
            <th className="px-4 py-2 border border-gray-400">Total Pack (Qty)</th>
            <th className="px-4 py-2 border border-gray-400">Grand Total</th>
            <th className="px-4 py-2 border border-gray-400">Action</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td className="text-center p-4" colSpan="5">
                <div className="flex justify-center mt-4">
                  <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="none" stroke="currentColor" strokeWidth="4" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Loading overall inventory...</span>
                </div>
              </td>
            </tr>
          ) : receipts.length === 0 ? (
            renderEmptyState()
          ) : (
            renderOverallInventoryTableRows()
          )}
        </tbody>
      </table>
      {/* Pagination Controls for Overall Inventory */}
      <div className="flex justify-between items-center mt-4">
        <button
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100"
          onClick={() => setOverallCurrentPage(overallCurrentPage - 1)}
          disabled={overallCurrentPage === 1}
        >
          Previous
        </button>
        <span>
          Page {overallCurrentPage} of {overallTotalPages}
        </span>
        <button
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-blue-100"
          onClick={() => setOverallCurrentPage(overallCurrentPage + 1)}
          disabled={overallCurrentPage === overallTotalPages || receipts.length === 0}
        >
          Next
        </button>
      </div>
    </div>
  )
  
  // Render input fields for a new product
  const renderUpdateItemRow = () => (
    <tr>
      <td className="px-4 py-2 border border-gray-300 text-center">N/A</td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        {newProduct.date ? (
          <input
            type="date"
            name="date"
            value={format(newProduct.date, 'yyyy-MM-dd')}
            onChange={e => setNewProduct({ ...newProduct, date: new Date(e.target.value) })}
            className={`w-full border border-gray-300 text-center p-1 ${errors.date ? 'border-red-500' : ''}`}
          />
        ) : (
          <input
            type="date"
            name="date"
            value=""
            onChange={e => setNewProduct({ ...newProduct, date: new Date(e.target.value) })}
            className={`w-full border border-gray-300 text-center p-1 ${errors.date ? 'border-red-500' : ''}`}
          />
        )}
        {errors.date && <span className="text-red-500 text-xs">{errors.date}</span>}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <select
          name="category_name"
          value={newProduct.category_name}
          onChange={e => setNewProduct({ ...newProduct, category_name: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.category_name ? 'border-red-500' : ''}`}
        >
          <option value="">Select</option>
          {categories.length > 0 ? (
            categories.map((category, index) => (
              <option key={index} value={category.category_name}>
                {category.category_name}
              </option>
            ))
          ) : (
            <option disabled>Create a category</option>
          )}
        </select>
        {errors.category_name && <span className="text-red-500 text-xs">{errors.category_name}</span>}
        {newProduct.category_name === "" && (
          <span className="text-red-500 text-xs">Please select a category</span>
        )}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="text"
          name="item_name"
          value={newProduct.item_name}
          onChange={e => setNewProduct({ ...newProduct, item_name: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.item_name ? 'border-red-500' : ''}`}
          placeholder="Item Name"
        />
        {errors.item_name && <span className="text-red-500 text-xs">{errors.item_name}</span>}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="number"
          name="qty_of_item"
          value={newProduct.qty_of_item}
          onChange={e => setNewProduct({ ...newProduct, qty_of_item: e.target.value })}
          min="1"
          className={`w-full border border-gray-300 text-center p-1 ${errors.qty_of_item ? 'border-red-500' : ''}`}
          placeholder="Quantity of Item"
        />
        {newProduct.qty_of_item === 1 ? 'item' : 'items'}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="number"
          name="qty_of_pack"
          value={newProduct.qty_of_pack}
          onChange={e => setNewProduct({ ...newProduct, qty_of_pack: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.qty_of_pack ? 'border-red-500' : ''}`}
          placeholder="Quantity of Pack"
        />
        {newProduct.qty_of_pack === 1 ? 'pack' : 'packs'}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="number"
          name="price"
          value={newProduct.price}
          onChange={e => setNewProduct({ ...newProduct, price: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.price ? 'border-red-500' : ''}`}
          placeholder="Price"
        />
        {errors.price && <span className="text-red-500 text-xs">{errors.price}</span>}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="number"
          name="totalAmount"
          value={newProduct.totalAmount}
          onChange={e => setNewProduct({ ...newProduct, totalAmount: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.totalAmount ? 'border-red-500' : ''}`}
          placeholder="Total Amount"
        />
        {errors.totalAmount && <span className="text-red-500 text-xs">{errors.totalAmount}</span>}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="text"
          name="store"
          value={newProduct.store}
          onChange={e => setNewProduct({ ...newProduct, store: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.store ? 'border-red-500' : ''}`}
          placeholder="Store"
        />
        {errors.store && <span className="text-red-500 text-xs">{errors.store}</span>}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <input
          type="text"
          name="receipt_number"
          value={newProduct.receipt_no}
          onChange={e => setNewProduct({ ...newProduct, receipt_no: e.target.value })}
          className={`w-full border border-gray-300 text-center p-1 ${errors.receipt_no ? 'border-red-500' : ''}`}
          placeholder="Receipt Number"
        />
        {errors.receipt_number && <span className="text-red-500 text-xs">{errors.receipt_no}</span>}
      </td>
      <td className="px-4 py-2 border border-gray-300 text-center">N/A</td>
      <td className="px-4 py-2 border border-gray-300 text-center">
        <button
          className="text-green-500 hover:text-green-700"
          onClick={() => handleSaveProduct(inventory[editingIndex].id)}
        >
          <FaSave />
        </button>
      </td>
    </tr>
  )

  return (
    <div className="w-full">
      {/* Overall Inventory Header and Stats */}
        {<div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-6">
          <h1 className="text-xl font-semibold mb-4">Inventory Overview</h1>
          <hr className="my-4 border-gray-300" />
          
          {/* Inventory Stats */}
          <div className="grid grid-cols-4 divide-x divide-gray-300">
            <div className="px-4">
              <h3 className="text-blue-500 text-medium font-bold flex items-center">
                <FaBox className="mr-2" /> Categories
              </h3>
              <p className="text-2xl font-bold">{totalCategories}</p>
              <span className="text-xs text-gray-400">Total Categories</span>
            </div>
            <div className="px-4">
              <h3 className="text-amber-500 text-medium font-bold flex items-center">
                <FaTag className="mr-2" /> Total Items
              </h3>
              <div className='flex justify-between'>
                <div className="text-left">
                  <p className="text-2xl font-bold">{totalProductsCount}</p>
                  <span className="text-xs text-gray-400">Total Items</span>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">₱{totalValue % 1 !== 0 ? totalValue.toFixed(2) : totalValue}</p>
                  <span className="text-xs text-gray-400">Total Value</span>
                </div>
              </div>
            </div>
            <div className="px-4">
              <h3 className="text-violet-500 text-medium font-bold flex items-center">
                <FaShippingFast className="mr-2" /> Restocks
              </h3>
              <div className='flex justify-between'>
                <div className="text-left">
                  <p className="text-2xl font-bold">{restocksCount}</p>
                  <span className="text-xs text-gray-400">Last 7 days</span>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">₱0</p>
                  <span className="text-xs text-gray-400">Total Cost</span>
                </div>
              </div>
            </div>
            <div className="px-4">
                <h3 className="text-red-500 text-medium font-bold flex items-center">
                    <FaExclamationTriangle className="mr-2" /> Low Stocks
                </h3>
                <div className='flex justify-between'>
                    <div className="text-left">
                        <p className="text-2xl font-bold">
                            {inventory.filter(product => product.qty_of_item <= STOCK_THRESHOLD).length}
                        </p>
                        <span className="text-xs text-gray-400">Products</span>
                    </div>
                    <div className="text-right">
                        <p className="text-2xl font-bold">
                            ₱{inventory.filter(product => product.qty_of_item <= STOCK_THRESHOLD).reduce((acc, product) => acc + parseFloat(product.totalAmount), 0) % 1 !== 0 ?
                                inventory.filter(product => product.qty_of_item <= STOCK_THRESHOLD).reduce((acc, product) => acc + parseFloat(product.totalAmount), 0).toFixed(2) :
                                inventory.filter(product => product.qty_of_item <= STOCK_THRESHOLD).reduce((acc, product) => acc + parseFloat(product.totalAmount), 0)}
                        </p>
                        <span className="text-xs text-gray-400">Total Value</span>
                    </div>
                </div>
            </div>
          </div>
        </div>}

        {/* Render Inventory Tabs */}
        <div className="w-full">
          {activeTab === '' && renderInventory()}
          {activeTab === 'overall' && renderOverallInventory()}
        </div>
    </div>
  )
}

export default InventoryProps
