import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { BsArrowLeftShort } from 'react-icons/bs';
import { MdError, MdCheckCircle } from 'react-icons/md';
import { api } from '../../api/apiService';

export const Registration = () => {
  const [email, setEmail] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();

  // Redirect to home page if user is already logged in
  // useEffect(() => {
  //   const authToken = localStorage.getItem('authToken');
  //   if (authToken) {
  //     navigate('/');
  //   }
  // }, [navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }
    
    if (!agreeToTerms) {
      setError('You must agree to the Terms of Use and Privacy Policy.');
      return;
    }
    
    try {
      const response = await api.post('/register', { email });
      const { token } = response.data;
      
      if (response.status === 200 && token) {
        localStorage.setItem('email', email);
        localStorage.setItem('authToken', token);
        setSuccessMessage('Registration successful! Please check your email for verification.');
        setError('');
        setTimeout(() => {
          navigate(`/register/verification-page/${token}`);
        }, 3000);
      }
    } catch (error) {
      setSuccessMessage('');
      if (error.response) {
        switch (error.response.status) {
          case 403:
            setError('The email registered is not for authorized personnel.');
            break;
          case 409:
            setError('This email is already registered.');
            break;
          case 422:
            setError('The email is already taken. Please use a different email.');
            break;
          default:
            setError('An unexpected error occurred. Please try again.');
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    }
  };

  // Effect to clear error when checkbox is checked
  useEffect(() => {
    if (agreeToTerms) {
      setError('');
    }
  }, [agreeToTerms]);

  return (
    <div className="flex flex-col min-h-screen bg-blue-950">
      <div className="flex-grow flex items-center justify-center">
        <div className="relative w-[540px] h-[600px] bg-neutral-100 rounded-lg shadow-md">
          <div className="absolute inset-0 px-[70px] py-[48px] flex flex-col justify-between">
            <div className="text-center mb-6">
              <img 
                src='https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s' 
                alt='company_logo' 
                className="mx-auto mb-4 w-20 h-20 object-contain"
              />
              <h2 className="text-2xl mb-4 font-bold">Create your account</h2>
            </div>
            <form onSubmit={handleSubmit} className="flex flex-col h-full">
              {/* Email Input */}
              <div className="relative mb-2 group">
                <label
                  className="absolute -top-3 left-3 px-1 text-sm font-medium text-gray-700 bg-neutral-100 z-10 group-focus-within:text-blue-600"
                  htmlFor="email"
                >
                  Email
                </label>
                <input 
                  type="email"
                  id="email"
                  autoComplete='email'
                  className="w-full px-4 pt-3 pb-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-600"
                  placeholder="Enter a valid email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              {/* Terms and Conditions Checkbox */}
              <div className={`mb-2 p-2 border rounded ${error ? 'border-red-500' : 'border-white'}`}>
                <label className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="agreeToTerms"
                    className="mr-2 h-4 w-4 align-middle"
                    checked={agreeToTerms} 
                    onChange={() => setAgreeToTerms(!agreeToTerms)} 
                  />
                  <span className='text-xs text-gray-700 align-middle'>
                    I have read and agree to our 
                    <Link to="/terms-of-use" className='text-black font-bold'> Terms of Use </Link> and 
                    <Link to="/privacy-policy" className='text-black font-bold'> Privacy Policy</Link>
                  </span>
                </label>
              </div>

              {/* Error Message */}
              {error && (
                <div className="flex items-center text-red-500 text-xs mb-2">
                  <MdError className="ml-1 mr-1 text-red-500" />
                  <p>{error}</p>
                </div>
              )}

              {/* Success Message */}
              {successMessage && (
                <div className="flex items-center text-green-500 text-xs mb-2">
                  <MdCheckCircle className="ml-1 mr-1 text-green-500" />
                  <p>{successMessage}</p>
                </div>
              )}

              {/* Submit Button */}
              <div className="mb-4">
                <button 
                  type="submit" 
                  className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600"
                >
                  Create account
                </button>
              </div>

              {/* Links and Divider */}
              <div className="text-center mb-4">
                <div className="text-black mb-2">
                  <p>Already have an account? <Link to="/login" className='text-blue-500 font-bold'>Sign in</Link></p>
                </div>
                <div className="flex items-center">
                  <hr className="flex-grow border-t border-gray-300" />
                  <span className="mx-4 text-gray-600">or</span>
                  <hr className="flex-grow border-t border-gray-300" />
                </div>
              </div>

              {/* Continue with Google Button */}
              <div>
                <button 
                  type="button" 
                  className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600"
                >
                  Continue with Google
                </button>
              </div>
            </form>

            {/* Back Arrow Icon */}
            <div className="absolute top-4 left-4">
              <Link to="/welcome" className="text-blue-500 text-4xl hover:text-blue-600">
                <BsArrowLeftShort />
              </Link>
            </div>
          </div>
        </div>
      </div>
      {/* Footer */}
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
