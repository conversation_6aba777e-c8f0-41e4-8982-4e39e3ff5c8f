<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Inventory;
use App\Models\Notification;
use Log;

class InventoryController extends Controller
{
    public function checkStockStatus($inventory)
    {
        if ($inventory->qty_of_item === 0) {
            return 'Out of Stock';
        } elseif ($inventory->qty_of_item <= 10) {
            return 'Low Stock';
        } else {
            return 'In-stock';
        }
    }

    public function showInventory()
    {
        $inventory = Inventory::get();
        return response()->json($inventory, 200);
    }

    public function updateInventory(Request $request, $id)
    {
      $request->validate([
        'date' => 'required|date',
        'category_name' => 'required|string|max:255',
        'item_name' => 'required|string|max:255',
        'qty_of_item' => 'required|integer|min:0',
        'qty_of_pack' => 'required|integer|min:0',
        'price' => 'required|numeric|min:0',
        'totalAmount' => 'required|numeric|min:0',
        'store' => 'required|string|max:255',
        'receipt_no' => 'required|string|max:255',
        'stock_status' => 'required|string',
      ]);
    
      try {
        $inventoryItem = Inventory::find($id);

        if (!$inventoryItem) {
            return response()->json(['error' => 'Inventory item not found'], 404);
        }

        // Update the inventory item
        $inventoryItem->update([
          'date' => $request->input('date'),
          'category_name' => $request->input('category_name'),
          'item_name' => $request->input('item_name'),
          'qty_of_item' => $request->input('qty_of_item'),
          'qty_of_pack' => $request->input('qty_of_pack'),
          'price' => $request->input('price'),
          'totalAmount' => $request->input('totalAmount'),
          'store' => $request->input('store'),
          'receipt_no' => $request->input('receipt_no'),
          'stock_status' => $request->input('stock_status'),
        ]);

        // Recalculate the stock status
        $inventoryItem->stock_status = $this->checkStockStatus($inventoryItem);

        // Save the updated stock status
        $inventoryItem->save();

        // Check if the stock status has changed to 'Low Stock'
        if ($inventoryItem->stock_status === 'Low Stock' && $inventoryItem->getOriginal('stock_status') !== 'Low Stock') {
          // Trigger a notification
          $this->triggerNotification($inventoryItem);
        }

        $receipt = $inventoryItem->receipt;
        $receipt->date = $request->input('date');
        $receipt->save();

        // Update the totals in the receipt
        $receipt = $inventoryItem->receipt;
        $receipt->updateTotals();
    
        return response()->json(['message' => 'Inventory item updated successfully.', 'inventory' => $inventoryItem], 200);
      } catch (\Exception $e) {
        return response()->json(['message' => $e->getMessage()], 404);
      }
    }
    
    private function triggerNotification(Inventory $inventoryItem)
    {
        // Create a notification message
        $message = "Inventory item {$inventoryItem->item_name} has low stock.";
    
        // Create and save the notification
        $notification = new Notification();
        $notification->message = $message;
        $notification->save();
    
        // Update the notification count
        $notifications = $this->getNotifications();
        $notificationCount = count($notifications);
    
        // Update the notification count for the user
        $user = auth()->user();
        $user->notification_count = $notificationCount;
        $user->save();
    
        return response()->json(['message' => 'Notification created successfully.'], 200);
    }

    public function createNotification(Request $request)
    {
        try {
            // Create a notification message
            $message = $request->input('message');
    
            // Create and save the notification
            $notification = new Notification();
            $notification->message = $message;
            $notification->save();
    
            // Update the notification count
            $notifications = $this->getNotifications();
            $notificationCount = $notifications->count();
    
            session()->put('notificationCount', $notificationCount);
    
            return response()->json(['message' => 'Notification created successfully.'], 200);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['message' => 'Error creating notification'], 500);
        }
    }

    public function getNotifications()
    {
        $notifications = Notification::latest()->get();
        return $notifications;
    }

    public function getNotificationCount()
    {
        $unreadNotifications = Notification::where('is_read', false)->count();
        $notificationCount = $unreadNotifications;
        return response()->json(['notificationCount' => $notificationCount]);
    }

    public function markNotificationAsRead(Request $request)
    {
        $notifications = $request->input('notifications');
        foreach ($notifications as $notification) {
            $notificationModel = Notification::find($notification['id']);
            if ($notificationModel) {
                $notificationModel->is_read = $notification['is_read'];
                $notificationModel->save();
            }
        }
        return response()->json(['message' => 'Notifications marked as read']);
    }

    public function removeAllNotifications(Request $request)
    {
      Notification::query()->delete();
      return response()->json(['message' => 'Notifications removed successfully']);
    }

    public function destroy($id)
    {
        $inventoryItem = Inventory::find($id); // Fetch by ID without user ID condition

        if (!$inventoryItem) {
            return response()->json(['message' => 'Inventory item not found.'], 404);
        }

        $inventoryItem->delete();

        return response()->json(['message' => 'Inventory item deleted successfully.'], 200);
    }
}
