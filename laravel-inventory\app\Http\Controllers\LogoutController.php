<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LogoutController extends Controller
{
    public function logout(Request $request)
    {
        // Get the currently authenticated user
        $user = Auth::user();
        
        if ($user) {
            // Revoke the token that was used to authenticate the current request
            $request->user()->token()->revoke();

            return response()->json([
                'message' => 'Successfully logged out. Token revoked.'
            ]);
        }

        return response()->json([
            'message' => 'No user authenticated.'
        ], 401);
    }
}
