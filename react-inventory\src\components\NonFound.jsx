import React from 'react';
import { Link } from 'react-router-dom';

export const NonFound = () => {
  return (
    <div className="relative min-h-screen bg-gray-100">
      {/* Header */}
      <header className="p-2 text-gray-800 shadow-md bg-blue-950">
        <div className="max-w-screen-xl mx-auto flex items-center justify-between">
          {/* Logo */}
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s"
            alt="Company Logo"
            className="w-16 h-16 object-contain"
          />
          <nav className="flex space-x-4 mr-5">
            <Link
              to="/login"
              className="text-white hover:text-gray-300 transition duration-300"
            >
              Login
            </Link>
            <Link
              to="/register"
              className="text-white hover:text-gray-300 transition duration-300"
            >
              Register
            </Link>
          </nav>
        </div>
      </header>

      {/* Not Found Message */}
      <div className="flex items-center justify-center min-h-[calc(100vh-136px)] py-16 bg-red-600">
        <div className="text-center p-12 bg-white rounded-lg shadow-md max-w-md mx-auto">
          <h2 className="text-4xl font-link mb-4">404 - Page Not Found</h2>
          <p className="text-gray-800 mb-6">
            Oops! The page you're looking for doesn't exist.
          </p>
          <Link 
            to="/welcome" 
            className="text-white bg-indigo-700 hover:bg-blue-600 px-4 py-2 rounded transition duration-300"
          >
            Go Back to Welcome Page
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
