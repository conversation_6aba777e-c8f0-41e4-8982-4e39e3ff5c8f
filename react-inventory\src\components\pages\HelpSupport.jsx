import React from "react";

export const HelpSupport = () => {
  return (
    <div className="p-4 w-full">
      <div className="flex flex-col items-center justify-start bg-gray-800 p-6">
        {/* Page Title */}
        <div className="flex justify-between ">
          <h1 className="text-3xl font-bold text-gray-100 mb-12">Help & Support</h1>
          <div className="flex-1"></div>
        </div>

        {/* Support Description */}
        <p className="text-2xl text-white mb-10 text-center max-w-6xl">
          We're here to assist you with any issues or
          questions you may have. Feel free to explore our frequently asked questions or
          contact our support team directly.
        </p>

        {/* FAQ and Contact Support side by side */}
        <div className="flex flex-col lg:flex-row w-full max-w-5xl space-y-8 lg:space-y-0 lg:space-x-20 mb-4">
          {/* Frequently Asked Questions */}
          <div className="w-full lg:w-5/6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-4">Frequently Asked Questions</h2>
            <hr className="my-4 border-gray-300" />
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-800">1. How do I reset my password?</h3>
                <p className="text-gray-600">
                  You can reset your password by navigating to the login page and clicking on "Forgot Password". Follow the instructions sent to your email.
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">2. How do I contact customer support?</h3>
                <p className="text-gray-600">
                  You can reach out to our support team via <NAME_EMAIL> or call us at 1-800-123-4567.
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">3. Where can I find the terms of service?</h3>
                <p className="text-gray-600">
                  You can view our terms of service by clicking <a href="/terms-of-use" className="text-blue-500 underline">here</a>.
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">4. Where can I find the data privacy policy?</h3>
                <p className="text-gray-600">
                  You can view our data privacy policy by clicking <a href="/privacy-policy" className="text-blue-500 underline">here</a>.
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-800">5. How do I report a bug or issue?</h3>
                <p className="text-gray-600">
                  You can report a bug or issue by sending an <NAME_EMAIL>.
                </p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="w-full lg:w-5/6 bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-4">Contact Support</h2>
            <hr className="my-4 border-gray-300" />
            <form className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-gray-700 font-medium">Your Name</label>
                <input
                  type="text"
                  id="name"
                  className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your name"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-gray-700 font-medium">Email</label>
                <input
                  type="email"
                  id="email"
                  className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label htmlFor="message" className="block text-gray-700 font-medium">Message</label>
                <textarea
                  id="message"
                  rows="4"
                  className="w-full p-2 mt-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Type your message here"
                ></textarea>
              </div>
              <button
                type="submit"
                className="w-full bg-blue-500 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-600 transition"
              >
                Submit
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
