<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

Class CreateReceiptsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
{
    Schema::create('receipts', function (Blueprint $table) {
        $table->id();
        $table->integer('item');
        $table->date('date');
        $table->integer('total_item');
        $table->integer('total_pack');
        $table->decimal('grand_total', 10, 2);
        $table->softDeletes();
        $table->timestamps();
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receipts');
    }
};
