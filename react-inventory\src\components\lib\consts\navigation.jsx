import { IoMdHome, IoMdSettings, IoIosLogOut } from "react-icons/io";
import { BsPersonCircle } from "react-icons/bs";
import { FaBoxOpen, FaCartPlus, FaStore } from "react-icons/fa";
import { HiDocumentReport, HiOutlineQuestionMarkCircle } from "react-icons/hi";

// Navigation links

// User view for the sidebar
export const DASHBOARD_SIDEBAR_LINKS = [
    {
        key: "dashboard",
        label: "Dashboard",
        path: "/",
        icon: <IoMdHome />
    },
    {
        key: "inventory",
        label: "Inventory",
        path: "/inventory",
        icon: <FaBoxOpen />
    },
    /* {
        key: "purchase",
        label: "Manage Purchase",
        path: "/purchase",
        icon: <FaCartPlus />
    }, */
    {
        key: "receipt",
        label: "Manage Receipt",
        path: "/receipt",
        icon: <HiDocumentReport />
    }
];

// Bottom links (shared)
export const DASHBOARD_SIDEBAR_BOTTOM_LINKS = [
    {
        key: "help-support",
        label: "Help & Support",
        path: "/help-support",
        icon: <HiOutlineQuestionMarkCircle />
    },
    
    {
        key: "logout",
        label: "Logout",
        path: "/login",
        icon: <IoIosLogOut />
    }
];

// Admin-only settings link
export const ADMIN_SIDEBAR_LINKS = [
    {
        key: "settings",
        label: "Settings",
        path: "/settings",
        icon: <IoMdSettings />
    }
];
