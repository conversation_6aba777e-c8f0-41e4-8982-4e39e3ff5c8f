import React, { useState } from 'react';
import { FaT<PERSON><PERSON><PERSON>n, <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaEnvelope, FaTrash, FaPlus } from 'react-icons/fa';
import { v4 as uuidv4 } from 'uuid';

const SettingsProps = ({
    users,
    toggleUserStatus,
    authorizedPersonnel,
    addAuthorizedPersonnel,
    removeAuthorizedPersonnel,
    activeTab,
}) => {
    const [newEmail, setNewEmail] = useState('');
    const [inputError, setInputError] = useState(false);
    const [emailTakenError, setEmailTakenError] = useState(false);

    // Handle adding authorized personnel
    const handleAddPersonnel = () => {
      setInputError(false);
      setEmailTakenError(false);
    
      if (newEmail.trim() === '') {
        setInputError(true);
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail)) {
        setInputError(true);
      } else if (authorizedPersonnel.some(person => person.email === newEmail)) {
        setEmailTakenError(true);
      } else {
        addAuthorizedPersonnel(newEmail);
        setNewEmail(''); // Clear input after adding
    
        // Call fetchAuthorizedPersonnel to update the authorizedPersonnel state
        props.fetchAuthorizedPersonnel();
      }
    };

    // Registered Users Section
    const renderRegisteredUsers = () => (
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300 mb-8">
            <h1 className="text-xl font-semibold mb-4">Registered Users</h1>
            <hr className="my-4 border-gray-300" />
            {users && users.length > 0 ? (
                <ul className="space-y-4">
                    {users.map(user => (
                        <li key={uuidv4()} className="flex items-center justify-between bg-gray-200 p-4 rounded-lg shadow-sm">
                            <div className="flex items-center space-x-4">
                                <FaUser className="text-blue-600 text-xl" />
                                <div>
                                    <p className="text-gray-800 font-semibold">{user.name}</p>
                                    <p className="text-gray-600 flex items-center">
                                        <FaEnvelope className="mr-2" />
                                        {user.email}
                                    </p>
                                    {user.status !== null ? (
                                        <p className={user.status === 'active' ? 'text-green-600' : 'text-red-600'}>
                                            Status: {user.status}
                                        </p>
                                    ) : (
                                        <p className="text-red-600">
                                            This user has not completed their registration.
                                        </p>
                                    )}
                                </div>
                            </div>
                            {user.status !== null && (
                                <div>
                                    <button className="text-2xl" onClick={() => toggleUserStatus(user.id)}>
                                        {user.status === 'active' ? (
                                            <FaToggleOn className="text-green-500" />
                                        ) : (
                                            <FaToggleOff className="text-red-500" />
                                        )}
                                    </button>
                                </div>
                            )}
                        </li>
                    ))}
                </ul>
            ) : (
                <div className="text-gray-600">No registered users to manage yet.</div>
            )}
        </div>
    );

    // Authorized Personnel Section
    const renderAuthorizedPersonnel = () => (
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
            <h1 className="text-xl font-semibold mb-4">Authorized Users</h1>
            <hr className="my-4 border-gray-300" />
            <div className="mb-2 flex space-x-4">
                <input
                    name='newEmail'
                    id='newEmail'
                    type="email"
                    value={newEmail}
                    onChange={(e) => {
                        setNewEmail(e.target.value);
                        setInputError(false);
                        setEmailTakenError(false);
                    }}
                    className={`p-2 border ${inputError || emailTakenError ? 'border-red-500' : 'border-gray-500'} rounded-md flex-grow`}
                    placeholder="Enter valid email to authorize"
                />
                <button onClick={handleAddPersonnel} className="p-3 bg-green-500 text-white rounded-md">
                    <FaPlus />
                </button>
            </div>

            {/* Error messages below the input */}
            <div className="mb-2 ml-2">
                {inputError && <p className="text-red-500">Email is required.</p>}
                {emailTakenError && <p className="text-red-500">Email is already taken.</p>}
            </div>

            {authorizedPersonnel && authorizedPersonnel.length > 0 ? (
                <ul className="space-y-4 mt-4">
                    {authorizedPersonnel.map(person => (
                        <li key={uuidv4()} className="flex items-center justify-between bg-gray-200 p-4 rounded-lg shadow-sm">
                            <div className="flex items-center space-x-4">
                                <FaEnvelope className="text-blue-600 text-xl" />
                                <div>
                                    <p className="text-gray-800 font-semibold">{person.email}</p>
                                </div>
                            </div>
                            <button className="text-red-500 text-xl" onClick={() => removeAuthorizedPersonnel(person.id)}>
                                <FaTrash />
                            </button>
                        </li>
                    ))}
                </ul>
            ) : (
                <div className="text-gray-600">No authorized users to manage yet.</div>
            )}
        </div>
    );

    return (
        <div className="w-full">
            {activeTab === 'users' && renderRegisteredUsers()}
            {activeTab === 'authorized' && renderAuthorizedPersonnel()}
        </div>
    );
};

export default SettingsProps;
