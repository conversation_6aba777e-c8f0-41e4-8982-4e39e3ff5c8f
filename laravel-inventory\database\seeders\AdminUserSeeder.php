<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if the admin user already exists to avoid duplication
        if (!User::where('email', '<EMAIL>')->exists()) {
            // Create the admin user
            $admin = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('Adminpassword!'), // Be sure to hash the password
                'is_admin' => 1,
                'status' => 'active', // Admin should be active by default
                'email_verified_at' => now(), // If you need email verification, mark it as verified
            ]);

            // Create an access token for the admin user
            $adminToken = $admin->createToken('AdminToken')->accessToken;

            // Save the token in the `users` table
            $admin->update(['token' => $adminToken]);

            // Output admin access token (optional for debugging)
            $this->command->info("Admin Token: {$adminToken}");
        }
    }
}
