<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePurchasesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::create('purchases', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('group_id', 36);
        //     $table->date('listed_date');
        //     $table->string('item_name', 255);
        //     $table->integer('qty_of_item');
        //     $table->integer('qty_of_pack');
        //     $table->softDeletes();
        //     $table->timestamps();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('purchases');
    }
}
