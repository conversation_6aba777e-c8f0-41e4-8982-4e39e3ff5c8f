<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Purchase;
use App\Models\Receipt;
use Ramsey\Uuid\Uuid;

class PurchaseController extends Controller
{
    public function storePurchase(Request $request)
    {
      $data = $request->validate([
        'purchases' => 'required|array',
        'purchases.*.listed_date' => 'required|date',
        'purchases.*.item_name' => 'required|string|max:255',
        'purchases.*.qty_of_item' => 'required|integer',
        'purchases.*.qty_of_pack' => 'required|integer',
      ]);
    
      // Generate a unique ID for the group of purchases
      $groupId = Uuid::uuid4()->toString();
    
      // Create purchases and store their IDs
      $purchaseIds = [];
      foreach ($data['purchases'] as $purchaseData) {
        $purchase = Purchase::create([
          'listed_date' => $purchaseData['listed_date'],
          'item_name' => $purchaseData['item_name'],
          'qty_of_item' => $purchaseData['qty_of_item'],
          'qty_of_pack' => $purchaseData['qty_of_pack'],
          'group_id' => $groupId,
        ]);
        $purchaseIds[] = $purchase->id;
      }
    
      // Create a receipt and store the group ID
      $receipt = Receipt::create([
          'receipt_date' => now(),
          'price' => 0,
          'totalAmount' => 0,
          'store' => '',
          'receipt_no' => '',
          'group_id' => $groupId,
      ]);
    
      // Return the purchase IDs and the group ID
      return response()->json(['message' => 'Purchases created successfully', 'purchases' => $purchaseIds, 'group_id' => $groupId, 'receipt_id' => $receipt->id], 200);
    }

    public function showPurchases(Request $request)
    {
      $groupId = $request->input('group_id');
      $purchases = Purchase::where('group_id', $groupId)->get();
      return response()->json(['purchases' => $purchases, 'group_id' => $groupId]);
    }
}
