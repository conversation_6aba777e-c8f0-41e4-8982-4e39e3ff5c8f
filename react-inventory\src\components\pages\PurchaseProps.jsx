/* import React, { useState } from 'react';
import { FaCalendarAlt, FaTag, FaBoxes, FaPlusCircle } from 'react-icons/fa';

const PurchaseProps = ({ listedDate, onAddPurchase, message, isError }) => {
  const [itemName, setItemName] = useState('');
  const [qtyOfItem, setQtyOfItem] = useState('');
  const [qtyOfPack, setQtyOfPack] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    const newItem = {
      listed_date: listedDate,
      item_name: itemName,
      qty_of_item: qtyOfItem,
      qty_of_pack: qtyOfPack,
    };
    onAddPurchase(newItem);
    // Reset form fields
    setItemName('');
    setQtyOfItem('');
    setQtyOfPack('');
  };

  return (
    <div className="mb-3 p-4 border border-gray-400 bg-gray-300">
      <h2 className="text-lg text-center font-bold mb-2">To Buy List</h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-4 flex border items-center">
          <FaCalendarAlt className="text-gray-500 mr-2" />
          <input
            type="date"
            value={listedDate}
            readOnly
            className="flex-1 p-2 text-center border border-gray-400 rounded"
            required
          />
        </div>
        <div className="mb-4 flex items-center">
          <FaTag className="text-gray-500 mr-2" />
          <input
            type="text"
            placeholder="Item Name"
            value={itemName}
            onChange={(e) => setItemName(e.target.value)}
            className="flex-1 p-2 text-center border border-gray-400 rounded"
            required
          />
        </div>
        <div className="mb-4 flex items-center">
          <FaBoxes className="text-gray-500 mr-2" />
          <input
            type="number"
            placeholder="Quantity of Item"
            value={qtyOfItem}
            onChange={(e) => setQtyOfItem(e.target.value)}
            className="flex-1 p-2 text-center border border-gray-400 rounded"
            required
          />
        </div>
        <div className="mb-4 flex items-center">
          <FaBoxes className="text-gray-500 mr-2" />
          <input
            type="number"
            placeholder="Quantity of Pack"
            value={qtyOfPack}
            onChange={(e) => setQtyOfPack(e.target.value)}
            className="flex-1 p-2 text-center border border-gray-400 rounded"
            required
          />
        </div>
        <div className="flex items-center justify-center mt-2">
          <button 
            type="submit" 
            className="flex items-center bg-blue-500 text-white p-2 rounded hover:bg-blue-600 transition"
          >
            <FaPlusCircle className="mr-2" />
            Add Purchase
          </button>
          {message && (
            <div className={`ml-2 text-center ${isError ? 'text-red-500' : 'text-green-500'}`}>
              {message}
            </div>
          )}
        </div>
      </form>
    </div>
  );
};

export default PurchaseProps;
 */