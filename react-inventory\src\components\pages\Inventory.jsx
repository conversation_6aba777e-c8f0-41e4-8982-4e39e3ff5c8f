import React, { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { FaSync } from 'react-icons/fa'
import { api } from '../../api/apiService'
import 'react-datepicker/dist/react-datepicker.css'
import InventoryProps from './InventoryProps'

export const Inventory = () => {
  const STOCK_THRESHOLD = 10
  const [inventory, setInventory] = useState([])
  const [filteredInventory, setFilteredInventory] = useState([])
  const [receipts, setReceipts] = useState([])
  const [categories, setCategories] = useState([])
  const [inventoryCurrentPage, setInventoryCurrentPage] = useState(1)
  const [totalCategories, setTotalCategories] = useState(0)
  const [loading, setLoading] = useState(true)
  const [editingIndex, setEditingIndex] = useState(null)
  const [newProduct, setNewProduct] = useState({
    date: new Date(),
    item_name: '',
    qty_of_item: '',
    qty_of_pack: '',
    price: '',
    totalAmount: '',
    store: '',
    receipt_no: '',
    stock_status: '',
  })
  const [errors, setErrors] = useState({})
  const [originalProduct, setOriginalProduct] = useState({})
  const [activeTab, setActiveTab] = useState('')

  const handleFilterChange = (filteredInventory) => {
   setFilteredInventory(filteredInventory)
  }

  useEffect(() => {
    const fetchInventory = async () => {
      try {
        const response = await api.get('/inventory')
        setInventory(response.data)
        setFilteredInventory(response.data)
        setLoading(false)
      } catch (error) {
        console.error('Error fetching inventory:', error)
        setLoading(false)
      }
    }
    fetchInventory()
  }, [])
  
  useEffect(() => {
    const fetchReceipts = async () => {
      try {
        const response = await api.get('/receipts')
        setReceipts(response.data)
        setLoading(false)
      } catch (error) {
        console.error('Error fetching receipts:', error)
        setLoading(false)
      }
    }
    fetchReceipts()
  }, [])

  useEffect(() => {
    api.get('/category')
      .then(response => {
        if (Array.isArray(response.data)) {
          setCategories(response.data)
          setTotalCategories(response.data.length)
        } else {
          console.error('Invalid response data')
        }
      })
      .catch(error => {
        console.error(error)
      })
  }, [])

  useEffect(() => {
    if (editingIndex !== null) {
      setOriginalProduct(inventory[editingIndex])
      setNewProduct({ ...inventory[editingIndex] })
    } else {
      resetForm()
    }
  }, [editingIndex, inventory])

  const handleEditProduct = (id) => {
    setEditingIndex(inventory.findIndex((product) => product.id === id))
  }

  const handleSaveProduct = async () => {
    const validationErrors = validateProduct(newProduct)
    if (Object.keys(validationErrors).length === 0) {
      const qty_of_item = parseInt(newProduct.qty_of_item, 10) || 0
  
      const productToSave = {
        ...newProduct,
        date: format(newProduct.date, 'yyyy-MM-dd'),
        qty_of_item,
      }
      
      try {
        if (editingIndex !== null) {
          const productId = inventory[editingIndex]?.id
          if (!productId) {
            console.error('Error: productId is not defined')
            return
          }
          await api.put(`/inventory/edit/${productId}`, productToSave)
          setInventory((prev) =>
            prev.map((prod, index) => (index === editingIndex ? productToSave : prod))
        )
        
        // Fetch the latest inventory data from the API
        const response = await api.get('/inventory')
        setInventory(response.data)
        setFilteredInventory(response.data)
        
          resetForm()
          
          if (productToSave.qty_of_item <= STOCK_THRESHOLD && originalProduct.qty_of_item > STOCK_THRESHOLD) {
            // Trigger the notification system
            await api.post('/create-notification', {
              message: `Inventory item ${productToSave.item_name} has low stock.`,
            })
          }
  
        } else {
          await api.put(`/inventory/edit/${productId}`, productToSave)
          setInventory(response.data)
          resetForm()
        }
        resetForm()
      } catch (error) {
        console.error('Error saving product:', error)
        setErrors((prev) => ({ ...prev, api: 'An error occurred while saving the product.' }))
      }
    } else {
      setErrors(validationErrors)
    }
  }
  
  const validateProduct = (product) => {
    const validationErrors = {}
    if (!product.item_name) validationErrors.item_name = "Product name is required."
  
    const price = parseFloat(product.price)
    if (isNaN(price) || price <= 0) validationErrors.price = "Price is required and must be a positive number."
    else if (price > 100000) validationErrors.price = "Price cannot exceed 100,000."
  
    const qty_of_item = parseInt(product.qty_of_item, 10)
    if (qty_of_item < 0 || isNaN(qty_of_item)) validationErrors.qty_of_item = "Quantity in item is required and must be a positive number or zero."
  
    const qty_of_pack = parseInt(product.qty_of_pack, 10)
    if (qty_of_pack < 0 || isNaN(qty_of_pack)) validationErrors.qty_of_pack = "Quantity in pack must be a positive number or zero."
  
    return validationErrors
  }
  
  const resetForm = () => {
    setNewProduct({ date: new Date(), category_name: '', item_name: '', qty_of_item: '', qty_of_pack: '', price: '', totalAmount: '', store: '', receipt_no: '', stock_status: 'In-stock' })
    setEditingIndex(null)
    setErrors({})
  }
  
  const hasChanges = JSON.stringify(newProduct) !== JSON.stringify(originalProduct)

  const handleTabChange = (tab) => setActiveTab(tab)

  return (
    <div className='p-4 w-full'>
      {/* Tab Navigation */}
      <div className="tabs flex space-x-3 mb-4 font-semibold">
        {['', 'overall'].map(tab => (
          <button
            key={tab}
            className={`px-4 py-2 rounded-md ${activeTab === tab ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-blue-100'}`}
            onClick={() => handleTabChange(tab)}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)} Inventory
          </button>
        ))}
        {/* Refresh Icon */}
        <div className="p-1.5 rounded-full bg-gray-100 transition duration-200">
          <button
            className="p-1.5 rounded-md text-gray-700 hover:text-blue-600"
            onClick={() => window.location.reload()}
          >
            <FaSync className="text-base" />
          </button>
        </div>
      </div>

      {loading ? (
        <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
          <div className="flex justify-center">
            <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="none" stroke="currentColor" strokeWidth="4" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading inventory...</span>
          </div>
        </div>
      ) : (
        <InventoryProps
          STOCK_THRESHOLD={STOCK_THRESHOLD}
          inventory={inventory}
          inventoryCurrentPage={inventoryCurrentPage}
          setInventoryCurrentPage={setInventoryCurrentPage}
          filteredInventory={filteredInventory}
          setFilteredInventory={setFilteredInventory}
          handleFilterChange={handleFilterChange}
          receipts={receipts}
          setReceipts={setReceipts}
          categories={categories}
          totalCategories={totalCategories}
          editingIndex={editingIndex}
          newProduct={newProduct}
          hasChanges={hasChanges}
          handleEditProduct={handleEditProduct}
          handleSaveProduct={handleSaveProduct}          
          errors={errors}
          resetForm={resetForm}
          activeTab={activeTab}
          restocksCount={inventory.filter(product => product.status === 'In-stock').reduce((acc, product) => acc + product.qty_of_item + product.qty_of_pack * product.qty_of_item, 0)}
          onChange={e => setNewProduct({ ...newProduct, date: new Date(e.target.value) })}
          setNewProduct={setNewProduct}
        />
      )}
    </div>
  )
}
