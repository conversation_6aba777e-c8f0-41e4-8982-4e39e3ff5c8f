<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Purchase extends Model
{
    use SoftDeletes;
    
    protected $fillable = ['group_id', 'listed_date', 'item_name', 'qty_of_item', 'qty_of_pack'];

    public function receipts()
    {
        return $this->belongsToMany(Receipt::class, 'purchase_receipt', 'purchase_id', 'receipt_id');
    }
}
