import React from 'react';
import { Link } from 'react-router-dom';

export const Welcome = () => {
  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Header */}
      <header className="p-2 bg-blue-950 shadow-md">
        <div className="max-w-screen-xl mx-auto flex items-center justify-between">
          {/* Logo */}
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRcOAJ4-7F2ucHEFp7gJnNUNaL30YnhvYltAw&s"
            alt="Company Logo"
            className="w-16 h-16 object-contain"
          />
          <nav className="flex space-x-4 mr-5">
            <Link
              to="/login"
              className="text-white hover:text-gray-300 transition duration-300"
            >
              Login
            </Link>
            <Link
              to="/register"
              className="text-white hover:text-gray-300 transition duration-300"
            >
              Register
            </Link>
          </nav>
        </div>
      </header>

      {/* Welcome Message */}
      <div className="flex items-center justify-center min-h-[calc(100vh-136px)] py-16 bg-sky-700">
        <div className="text-center p-12 bg-white rounded-lg shadow-md max-w-md mx-auto">
          <h2 className="text-4xl font-link mb-4">Welcome to Fligno Inventory Management</h2>
          <p className="text-gray-800">
            Manage your inventory efficiently with our app. Get started by logging in or registering for an account.
          </p>
        </div>
      </div>

      {/* Footer */}
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
