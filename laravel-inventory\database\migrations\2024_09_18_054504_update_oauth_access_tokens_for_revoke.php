<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOauthAccessTokensForRevoke extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            // Adding 'revoked' column if it doesn't exist
            if (!Schema::hasColumn('oauth_access_tokens', 'revoked')) {
                $table->boolean('revoked')->default(false)->after('expires_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oauth_access_tokens', function (Blueprint $table) {
            if (Schema::hasColumn('oauth_access_tokens', 'revoked')) {
                $table->dropColumn('revoked');
            }
        });
    }
}
