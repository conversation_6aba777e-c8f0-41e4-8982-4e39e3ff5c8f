<?php

namespace App\Providers;

use App\Http\Middleware\AdminMiddleware;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Router;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(Router $router)
    {
        // Add custom validation rule
        Validator::extend('email_domain', 'App\Rules\EmailDomain@passes');

        // Register the AdminMiddleware
        $router->aliasMiddleware('admin', AdminMiddleware::class);
    }
}
