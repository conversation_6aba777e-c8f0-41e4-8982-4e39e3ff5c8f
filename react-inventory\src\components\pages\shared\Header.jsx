import React, { useState, useEffect, Fragment } from 'react';
import { HiOutlineSearch, HiOutlineBell, HiOutlineCheckCircle, HiOutlineTrash } from 'react-icons/hi';
import { Popover, Transition, PopoverPanel, PopoverButton, Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/react';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../api/apiService';

export const Header = ({ isAdmin }) => {
  const navigate = useNavigate();
  const [error, setError] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await api.get('/notifications');
        setNotifications(response.data);
        // Fetch the notification count from the backend
        const notificationCountResponse = await api.get('/notification-count');
        setNotificationCount(notificationCountResponse.data.notificationCount);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      }
    };
  
    fetchNotifications();
  }, []);

  useEffect(() => {
    const fetchNotificationCount = async () => {
      const notificationCountResponse = await api.get('/notification-count');
      setNotificationCount(notificationCountResponse.data.notificationCount);
    };
    fetchNotificationCount();
  }, []);

  useEffect(() => {
    const notificationCount = notifications.filter((notification) => !notification.is_read).length;
    setNotificationCount(notificationCount);
  }, [notifications]);

  const handleMarkAllAsRead = async () => {
    try {
      // Identify the notifications to be marked
      const updatedNotifications = notifications.map((notification) => {
        // If there are any unread notifications, mark them as read first
        if (!notifications.some((n) => !n.is_read)) {
          // If all notifications are already read, toggle their state
          return {
            ...notification,
            is_read: !notification.is_read,
          };
        } else {
          // If there are unread notifications, mark them as read
          return {
            ...notification,
            is_read: true,
          };
        }
      });
  
      // Send the updated notifications to the backend
      await api.post('/notifications/mark-as-read', { notifications: updatedNotifications });
      setNotifications(updatedNotifications);
  
      // Update the notification count
      setNotificationCount(updatedNotifications.filter((notification) => !notification.is_read).length);
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  };

  const handleRemoveAllNotifications = async () => {
    try {
      await api.post('/notifications/remove-all');
      setNotifications([]);
      setNotificationCount(0);
    } catch (error) {
      console.error('Error removing notifications:', error);
    }
  };
  
  const handleLogout = async () => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      try {
        await api.post('/logout', {}, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        localStorage.removeItem('authToken');
        navigate('/login');
      } catch (error) {
        console.error("Error logging out:", error);
        setError('Failed to log out.');
      }
    } else {
      navigate('/login');
    }
  };

  return (
    <div className='bg-white h-20 px-4 flex justify-between items-center border-b border-gray-200'>
      <div className='relative'>
        {/* <HiOutlineSearch fontSize={20} className='text-gray-400 absolute top-1/2 -translate-y-1/2 left-3' />
        <input 
          id='default-search'
          type='text' 
          placeholder='Search product, item, store...' 
          className='text-sm focus:outline-none h-10 w-[24rem] border border-gray-500 rounded-sm pl-11 pr-4'
        /> */}
      </div>
      <div className='flex items-center gap-x-2'>
        <Popover className="relative">
          {({ open }) => (
            <>
              <PopoverButton className={classNames(open && 'bg-gray-100', "p-2 rounded-sm inline-flex items-center text-gray-700")}>
                <HiOutlineBell fontSize={28} />
                {notificationCount > 0 && (
                  <span className="absolute top-0 right-0 bg-red-600 text-white text-xs rounded-full px-1.5 py-0.5">
                    {notificationCount > 99 ? '...' : notificationCount}
                  </span>
                )}
              </PopoverButton>
              <Transition as={Fragment} enter="transition ease-out duration-200" enterFrom="opacity-0 translate-y-1" enterTo="opacity-100 translate-y-0" leave="transition ease-in duration-150" leaveFrom="opacity-100 translate-y-0" leaveTo="opacity-0 translate-y-1">
                <PopoverPanel className="absolute right-0 z-10 mt-2.5 w-80">
                  <div className="bg-white rounded-sm shadow-md ring-1 ring-black ring-opacity-5 px-2 py-2.5">
                    <div className="header-container flex justify-between items-center py-2 px-2 border-b border-gray-200">
                      <strong className="text-gray-700 font-medium">Notifications</strong>
                      <div className="flex gap-x-1">
                        <button 
                          onClick={handleMarkAllAsRead}
                          className="text-gray-700 hover:bg-gray-100 rounded p-1"
                          aria-label="Mark all as read"
                          title="Mark All as Read"
                        >
                          <HiOutlineCheckCircle className='text-2xl cursor-pointer' />
                        </button>
                        <button 
                          onClick={handleRemoveAllNotifications}
                          className="text-gray-700 hover:bg-gray-100 rounded p-1"
                          aria-label="Remove all notifications"
                          title="Remove All Notifications"
                        >
                          <HiOutlineTrash className='text-2xl cursor-pointer' />
                        </button>
                      </div>
                    </div>
                    <div className="content-container p-2" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                      <div className="mt-2 py-1 text-sm">
                        {notifications.length > 0 ? (
                          notifications.map((notification) => (
                            <div key={notification.id} className={notification.is_read ? 'text-gray-500' : 'bg-gray-100'}>
                              <p>{notification.message}</p>
                              <p>{new Date(notification.created_at).toLocaleString()}</p>
                              <hr className="my-2 border-gray-300" />
                            </div>
                          ))
                        ) : (
                          <p>No notifications yet.</p>
                        )}
                      </div>
                    </div>
                  </div>
                </PopoverPanel>
              </Transition>
            </>
          )}
        </Popover>
        <Menu as="div" className="relative">
          <MenuButton className="items-center p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-neutral-400">
            <span className="sr-only">Open user menu</span>
            <div className="h-10 w-10 rounded-full bg-sky-500 bg-cover bg-no-repeat bg-center" style={{ backgroundImage: 'url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAAAPFBMVEXk5ueutLepsLPo6uursbXJzc/p6+zj5ea2u76orrKvtbi0ubzZ3N3O0dPAxcfg4uPMz9HU19i8wcPDx8qKXtGiAAAFTElEQVR4nO2d3XqzIAyAhUD916L3f6+f1m7tVvtNINFg8x5tZ32fQAIoMcsEQRAEQRAEQRAEQRAEQRAEQRAEQRAEQRAEQRAEQTghAJD1jWtnXJPP/54IgNzZQulSmxvTH6oYXX4WS+ivhTbqBa1r26cvCdCu6i0YXbdZ0o4A1rzV+5IcE3YE+z58T45lqo7g1Aa/JY5tgoqQF3qb382x7lNzBLcxft+O17QUYfQI4IIeklKsPSN4i6LKj/7Zm8n99RbHJpEw9gEBXNBpKIYLJqKYRwjOikf//r+J8ZsVuacbqCMNleI9TqGLGqMzhnVdBOdd6F/RlrFijiCoVMk320CBIahUxTWI0KKEcJqKbMdpdJb5QvdHq6wCI5qhKlgGMS/RBHkubWDAE+QZxB4xhCyDiDkLZxgGEVdQldzSKbTIhmZkFkSEPcVvmBn2SMuZB9od7fQDsMiDdKJjFUSCQarM5WirZ3C2TT/htYnyPcPfgrFHWz0BI74gr6J/IZiGUxAZGQLqmvQLTrtE/Go4YxhVRIpEw+sww1IIcqr5NKmUUzLF3d4/qPkYIp2T/obPuemlojFUR4t9Q2Vojhb7BmgElWHzLPH8hucfpefPNFTVgs9h1AdU/Pin96vwWbWdf+X9Absn3OdO34aMdsDnP8WgKYisTqI6CkNGqZQo1XA6Ef6AU32SJzOcBukHPF07/xNSgmHKa5BOhtezv6mA/rYJpwXNAnbRZ1XuF3BzDcO3vpA3+ny2909gbqE4hhD3LIPhLLyBNhPZvbZ3B+3tPYa18A7auSlXQayKwTPNLKDcuOB0xPYKDPFTkWsevQPRZ1J8Hji9I1KQ34r7hZhrwNwOZ97QxNx0drwn4QI0wQk1DcEsfKCWKdxVvxPSNUIp/knmAXT+nT+Ko3+0H96rcNb3m1fx7MBTJdeBJ7uFcWsc0wvgAsC4pROW0l2inbAmIBv/7GZmuhQH6API2rr8T0e6yuZJ+80A9LZeG62T3tik31XwxtwZcizKuTHkMjB1WdZde4Kmic/A5ZI3rr1ae21d08PlVHYfAaxw9G9CYRbJ+8ZdbTcMRV1XM3VdF0M32vtoTdZ0+u29s0OttJ5bz64UwinjaFMVY9vkqc3KKSxN21Xl+0L4Q3Vuv1tYl0pqnX6ms4XetFz7gdZVAgUEoJntfOUe4ZwsHd9FzqQ3Vv6xe41l0XJcqcKl6TZvlv7ClAW3BsqQW4X7ypApB8dmTgK4IX5wvqIVj33HtD2qSG4BqznxdIefL27Y4sahi0MdIdvUsDva8agGGbCtITmCY31MHD2O0uIdh/0rJDQ1VX5Zdxz3rR2QDbv6qXl9vudzqQtGm1Jv9LDXOsfvvB7VcZ8PDKD0mQ1VHPYQ9O+Yj4hR1IUD8rBnn3ho2m8oQMxbCFiKlL2ioSW5heeJqegED52CzxCtcGD3Kv8Wms9EYLyUhwaFIhSMBClevWEmiK/Iaogu4H7sg6ppQhQG8RUqivuTGOAJOg6FfgW0q0M0PQMRMEgXaeNf3SYDZ8PIMI0+wHgr/MgN7wYwpiLjCCqM6ydUDZLQiB6nDdNC8SDyig3jPPpFXGcC9O8BUBDVmgBY59E7Md/35Loe/UVEECEJwYggJjELZ4J71SaQSBeC02n4Da29CayJNA28SAhd2CQyC1Xw6pSmGSINQVuMhAZp4DClan9MgmkDDNmezqwS8sgtlXK/EPBhoaSmYVC/F7IO1jQEdHOlabpKh3+jzLQSTUiq4X2I+Ip/zU8rlaqAvkS21ElR+gqu3zbjjL+hIAiCIAiCIAiCIAiCsCf/AKrfVhSbvA+DAAAAAElFTkSuQmCC")' }}>
              <span className='sr-only'>Account Name</span>
            </div>
          </MenuButton>
          <Transition as={Fragment} enter="transition ease-out duration-100" enterFrom="transform opacity-0 scale-95" enterTo="transform opacity-100 scale-100" leave="transition ease-in duration-75" leaveFrom="opacity-100 scale-100" leaveTo="opacity-0 scale-95">
            <MenuItems className="origin-top-right z-10 absolute right-0 mt-2 w-48 rounded-sm shadow-md p-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
              {isAdmin ? (
                <MenuItem>
                  {({ selected }) => (
                    <div className={classNames(selected && 'bg-gray-100', 'text-gray-700 hover:bg-gray-200 cursor-pointer px-4 py-2')} onClick={() => navigate('/settings')}>
                      Settings
                    </div>
                  )}
                </MenuItem>
              ) : null}
              <MenuItem>
                {({ selected }) => (
                  <div className={classNames(selected && 'bg-gray-100', 'text-gray-700 hover:bg-gray-200 cursor-pointer px-4 py-2')} onClick={handleLogout}>
                    Sign Out
                  </div>
                )}
              </MenuItem>
            </MenuItems>
          </Transition>
        </Menu>
      </div>
    </div>
  );
};
