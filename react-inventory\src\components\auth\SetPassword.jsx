import React, { useState, useEffect } from 'react';
import { HiEyeSlash, HiEye } from "react-icons/hi2";
import { MdError } from "react-icons/md";
import { useParams, useNavigate } from 'react-router-dom';
import { api } from '../../api'; 

export const SetPassword = () => {
  const { token, verificationCode } = useParams();
  const [showPassword, setShowPassword] = useState(false);
  const [pass1, setPass1] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState('');
  const navigate = useNavigate();

  // Validate token and verification code
  useEffect(() => {
    const validateTokenAndCode = async () => {
      const storedEmail = localStorage.getItem('email');
      if (storedEmail) {
        setEmail(storedEmail); // Set email state here
        try {
          const response = await api.post('/verify', {
            token,
            code: verificationCode, // Change this from verification_code to code
            email: storedEmail,
          });
  
          if (response.status === 200) {
            setIsValid(true);
          } else {
            setError('Invalid token or verification code');
          }
        } catch (error) {
          console.log('Error:', error.response?.data);
          setError(error.response?.data.message || 'An error occurred. Please try again.');
        } finally {
          setLoading(false);
        }
      } else {
        setError('Email not found. Please verify your email.');
        setLoading(false);
      }
    };
  
    validateTokenAndCode();
  }, [token, verificationCode]);  

  const validatePassword = () => {
    if (pass1.length < 8) return 'Your password must be at least 8 characters long.';
    if (!/[A-Z]/.test(pass1)) return 'Your password must contain at least one uppercase letter.';
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(pass1)) return 'Your password must contain at least one special character.';
    return '';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const errorMsg = validatePassword();
    if (errorMsg) {
      setError(errorMsg);
      return;
    }
  
    setIsSubmitting(true);
    setError('');
  
    try {
      const response = await api.post('/set-password', {
        email,
        password: pass1,
        verification_code: verificationCode,
        token,
      });
  
      if (response.status === 200) {
        // Clear the token and email from localStorage
        localStorage.removeItem('email');
        localStorage.removeItem('authToken'); // Remove the token here
        navigate('/login'); // Navigate to the login page after setting the password
      }
    } catch (error) {
      setIsSubmitting(false);
      setError(error.response?.data.message || 'An error occurred. Please try again.');
    }
  };  

  const handleInputChange = (e) => {
    setPass1(e.target.value);
  };

  const handleBackClick = () => {
    navigate(-1); // Go back to the previous page
  };

  // Show loading state if validating token
  if (loading) return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  // Show error if not valid
  if (!isValid) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div>Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-blue-950">
      <div className="flex-grow flex items-center justify-center">
        <div className="relative w-[540px] h-[540px] bg-white rounded-lg shadow-md border">
          <div className="absolute inset-0 px-[70px] py-[48px] flex flex-col justify-start space-y-6">
            <button
              onClick={handleBackClick}
              className="text-left text-black mb-4 font-medium hover:underline"
            >
              <span>&lt; Back</span>
            </button>
            <h2 className="text-2xl font-bold mb-1">Set password</h2>
            <p className="text-gray-600">
              Password requires a minimum of 8 characters and contains a mix of letters, numbers, and symbols.
            </p>
            <div className="relative mb-2 group">
              <label
                className="absolute -top-3 left-3 px-1 text-sm font-medium text-gray-700 bg-white z-10 group-focus-within:text-blue-600"
                htmlFor="pass1"
              >
                Password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                name="pass"
                id="pass1"
                value={pass1}
                onChange={handleInputChange}
                className={`w-full pr-10 px-4 pt-4 pb-4 text-gray-900 bg-white border-2 rounded-lg focus:outline-none ${error ? 'border-red-500' : 'border-black'} focus:border-blue-600`}
                disabled={isSubmitting}
                autoComplete="new-password"
              />
              {pass1 && (
                <div
                  className="absolute inset-y-0 right-3 flex items-center cursor-pointer"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <HiEye className="text-gray-500" size={24} /> : <HiEyeSlash className="text-gray-500" size={24} />}
                </div>
              )}
            </div>
            {error && (
              <div className="flex items-center text-red-500 mb-4">
                <MdError className="mr-2" size={24} />
                <span>{error}</span>
              </div>
            )}
            <button
              type="submit"
              onClick={handleSubmit}
              className="w-full py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Continue"}
            </button>
          </div>
        </div>
      </div>
      <footer className="text-center py-4 bg-gray-800 text-white">
        <p>&copy; {new Date().getFullYear()} Fligno Inventory Management. All rights reserved.</p>
      </footer>
    </div>
  );
};
